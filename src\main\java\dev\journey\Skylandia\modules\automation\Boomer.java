package dev.journey.Skylandia.modules.automation;

import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.render.Render2DEvent;

import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.systems.friends.Friends;
import dev.journey.Skylandia.Skylandia;

import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.item.ItemStack;
import net.minecraft.item.ArmorItem;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.Hand;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket;
import net.minecraft.network.packet.c2s.play.HandSwingC2SPacket;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;

import java.util.List;
import java.util.ArrayList;

public class Boomer extends Module {
    // Settings groups
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTargeting = settings.createGroup("Targeting");
    private final SettingGroup sgPlacement = settings.createGroup("Placement");
    private final SettingGroup sgBreaking = settings.createGroup("Breaking");
    private final SettingGroup sgMultiCrystal = settings.createGroup("Multi-Crystal");
    private final SettingGroup sgDamage = settings.createGroup("Damage");
    private final SettingGroup sgRender = settings.createGroup("Render");
    private final SettingGroup sgFailsafe = settings.createGroup("Failsafe");

    // Enums for multi-crystal functionality
    public enum MultiCrystalMode {
        Disabled("Disabled"),
        Dual("Dual Crystal"),
        Triple("Triple Crystal"),
        Quad("Quad Crystal"),
        Adaptive("Adaptive");

        private final String name;
        MultiCrystalMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    public enum PlacementPattern {
        Surrounding("Surrounding"),
        Linear("Linear"),
        Cross("Cross Pattern"),
        Diamond("Diamond"),
        Optimal("Optimal Damage");

        private final String name;
        PlacementPattern(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    // General settings
    private final Setting<Boolean> autoSwitch = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-switch")
        .description("Automatically switch to crystals when needed.")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> sequential = sgGeneral.add(new BoolSetting.Builder()
        .name("sequential")
        .description("Enable sequential placement/breaking.")
        .defaultValue(false)
        .build());

    private final Setting<Boolean> rotate = sgGeneral.add(new BoolSetting.Builder()
        .name("rotate")
        .description("Rotate to crystal/block before action.")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> antiCheat = sgGeneral.add(new BoolSetting.Builder()
        .name("anti-cheat")
        .description("Anti-cheat compatible timing and behavior.")
        .defaultValue(true)
        .build());

    // Targeting settings
    private final Setting<Double> targetRange = sgTargeting.add(new DoubleSetting.Builder()
        .name("target-range")
        .description("Maximum range to search for targets.")
        .defaultValue(10.0)
        .min(1)
        .max(20)
        .sliderMax(15)
        .build());

    private final Setting<Boolean> ignoreFriends = sgTargeting.add(new BoolSetting.Builder()
        .name("ignore-friends")
        .description("Don't target friends.")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> smartTarget = sgTargeting.add(new BoolSetting.Builder()
        .name("smart-target")
        .description("Target the player with lowest health/armor.")
        .defaultValue(true)
        .build());

    // Placement settings
    private final Setting<Integer> placeDelay = sgPlacement.add(new IntSetting.Builder()
        .name("place-delay")
        .description("Delay between placements (ms).")
        .defaultValue(50)
        .min(0)
        .max(1000)
        .sliderMax(500)
        .build());

    private final Setting<Double> placeRange = sgPlacement.add(new DoubleSetting.Builder()
        .name("place-range")
        .description("Range for crystal placement.")
        .defaultValue(5.0)
        .min(1)
        .max(10)
        .sliderMax(8)
        .build());

    private final Setting<Boolean> strictDirection = sgPlacement.add(new BoolSetting.Builder()
        .name("strict-direction")
        .description("Only place crystals on obsidian/bedrock.")
        .defaultValue(true)
        .build());

    // Breaking settings
    private final Setting<Integer> breakDelay = sgBreaking.add(new IntSetting.Builder()
        .name("break-delay")
        .description("Delay between breaking crystals (ms).")
        .defaultValue(50)
        .min(0)
        .max(1000)
        .sliderMax(500)
        .build());

    private final Setting<Double> breakRange = sgBreaking.add(new DoubleSetting.Builder()
        .name("break-range")
        .description("Range for crystal breaking.")
        .defaultValue(5.0)
        .min(1)
        .max(10)
        .sliderMax(8)
        .build());

    private final Setting<Boolean> inhibit = sgBreaking.add(new BoolSetting.Builder()
        .name("inhibit")
        .description("Break own crystals to prevent enemy use.")
        .defaultValue(true)
        .build());

    // Multi-Crystal settings  
    private final Setting<MultiCrystalMode> multiCrystalMode = sgMultiCrystal.add(new EnumSetting.Builder<MultiCrystalMode>()
        .name("multi-crystal-mode")
        .description("Deploy multiple crystals simultaneously.")
        .defaultValue(MultiCrystalMode.Dual)
        .build());

    private final Setting<PlacementPattern> placementPattern = sgMultiCrystal.add(new EnumSetting.Builder<PlacementPattern>()
        .name("placement-pattern")
        .description("Pattern for multi-crystal placement.")
        .defaultValue(PlacementPattern.Optimal)
        .build());

    private final Setting<Integer> maxCrystals = sgMultiCrystal.add(new IntSetting.Builder()
        .name("max-crystals")
        .description("Maximum crystals to place per tick.")
        .defaultValue(2)
        .min(1)
        .max(5)
        .sliderMax(4)
        .build());

    private final Setting<Double> multiRange = sgMultiCrystal.add(new DoubleSetting.Builder()
        .name("multi-range")
        .description("Range between multiple crystal placements.")
        .defaultValue(3.0)
        .min(1)
        .max(8)
        .sliderMax(6)
        .build());

    private final Setting<Integer> multiDelay = sgMultiCrystal.add(new IntSetting.Builder()
        .name("multi-delay")
        .description("Delay between multiple placements (ticks).")
        .defaultValue(1)
        .min(0)
        .max(10)
        .sliderMax(5)
        .build());
    // Damage settings
    private final Setting<Double> minDamage = sgDamage.add(new DoubleSetting.Builder()
        .name("min-damage")
        .description("Minimum damage to target.")
        .defaultValue(6.0)
        .min(0)
        .max(20)
        .sliderMax(15)
        .build());

    private final Setting<Double> maxSelfDamage = sgDamage.add(new DoubleSetting.Builder()
        .name("max-self-damage")
        .description("Maximum allowed self damage.")
        .defaultValue(8.0)
        .min(0)
        .max(20)
        .sliderMax(15)
        .build());

    private final Setting<Boolean> armorCheck = sgDamage.add(new BoolSetting.Builder()
        .name("armor-check")
        .description("Consider target armor in damage calculation.")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> lethalCheck = sgDamage.add(new BoolSetting.Builder()
        .name("lethal-check")
        .description("Prevent lethal self-damage.")
        .defaultValue(true)
        .build());

    private final Setting<Double> healthThreshold = sgDamage.add(new DoubleSetting.Builder()
        .name("health-threshold")
        .description("Stop attacking when self health is below this.")
        .defaultValue(4.0)
        .min(0)
        .max(20)
        .sliderMax(15)
        .build());

    // Render settings
    private final Setting<Boolean> renderPlacement = sgRender.add(new BoolSetting.Builder()
        .name("render-placement")
        .description("Render placement positions.")
        .defaultValue(true)
        .build());

    private final Setting<SettingColor> placementColor = sgRender.add(new ColorSetting.Builder()
        .name("placement-color")
        .description("Color for placement positions.")
        .defaultValue(new SettingColor(255, 0, 0, 100))
        .build());

    private final Setting<Boolean> renderBreaking = sgRender.add(new BoolSetting.Builder()
        .name("render-breaking")
        .description("Render breakable crystals.")
        .defaultValue(true)
        .build());

    private final Setting<SettingColor> breakingColor = sgRender.add(new ColorSetting.Builder()
        .name("breaking-color")
        .description("Color for breakable crystals.")
        .defaultValue(new SettingColor(255, 255, 0, 100))
        .build());

    private final Setting<Boolean> renderDamage = sgRender.add(new BoolSetting.Builder()
        .name("render-damage")
        .description("Show damage values.")
        .defaultValue(true)
        .build());

    // Failsafe settings (made less aggressive)
    private final Setting<Boolean> enableFailsafe = sgFailsafe.add(new BoolSetting.Builder()
        .name("enable-failsafe")
        .description("Enable failsafe systems.")
        .defaultValue(false)
        .build());

    private final Setting<Integer> maxAttempts = sgFailsafe.add(new IntSetting.Builder()
        .name("max-attempts")
        .description("Maximum attempts before giving up.")
        .defaultValue(20)
        .min(5)
        .max(100)
        .sliderMax(50)
        .visible(enableFailsafe::get)
        .build());

    private final Setting<Boolean> pauseOnLowHealth = sgFailsafe.add(new BoolSetting.Builder()
        .name("pause-on-low-health")
        .description("Pause when health is critically low.")
        .defaultValue(true)
        .build());

    // State variables
    private long lastPlaceTime = 0;
    private long lastBreakTime = 0;
    private int placeAttempts = 0;
    private int breakAttempts = 0;
    private PlayerEntity target = null;
    private final List<BlockPos> placementPositions = new ArrayList<>();
    private final List<EndCrystalEntity> breakableCrystals = new ArrayList<>();
    private int crystalsPlacedThisTick = 0;

    public Boomer() {
        super(Skylandia.Automation, "boomer", "Advanced crystal combat automation with multi-crystal support.");
    }

    @Override
    public void onActivate() {
        if (mc.player == null || mc.world == null) {
            error("Player or world is null!");
            toggle();
            return;
        }

        // Reset state variables
        placementPositions.clear();
        breakableCrystals.clear();
        lastPlaceTime = 0;
        lastBreakTime = 0;
        placeAttempts = 0;
        breakAttempts = 0;
        target = null;
        crystalsPlacedThisTick = 0;

        info("Boomer activated - Multi-crystal mode: %s", multiCrystalMode.get().toString());
    }

    @Override
    public void onDeactivate() {
        // Clean shutdown
        placementPositions.clear();
        breakableCrystals.clear();
        lastPlaceTime = 0;
        lastBreakTime = 0;
        placeAttempts = 0;
        breakAttempts = 0;
        target = null;
        crystalsPlacedThisTick = 0;
        
        info("Boomer deactivated");
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // Check for critical health pause
        if (pauseOnLowHealth.get() && mc.player.getHealth() <= healthThreshold.get()) {
            return;
        }

        // Reset crystals placed counter each tick
        crystalsPlacedThisTick = 0;

        // Find target
        target = findTarget();
        if (target == null) {
            placeAttempts = 0;
            breakAttempts = 0;
            return;
        }

        // Check if we have crystals for placement
        if (!hasCrystals()) {
            if (autoSwitch.get()) {
                switchToCrystals();
            }
            return;
        }

        // Handle crystal breaking first (priority)
        handleBreaking();

        // Handle crystal placement
        handlePlacement();

        // Update failsafe counters (only if enabled)
        if (enableFailsafe.get()) {
            if (placeAttempts >= maxAttempts.get() || breakAttempts >= maxAttempts.get()) {
                warning("Failsafe triggered - too many attempts");
                toggle();
            }
        }
    }

    private PlayerEntity findTarget() {
        if (mc.world == null || mc.player == null) return null;

        PlayerEntity bestTarget = null;
        double bestScore = Double.MAX_VALUE;
        double maxRange = targetRange.get();

        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof PlayerEntity player)) continue;
            if (player == mc.player) continue;
            if (player.isDead() || player.getHealth() <= 0) continue;
            if (ignoreFriends.get() && Friends.get().isFriend(player)) continue;

            double distance = mc.player.squaredDistanceTo(player);
            if (distance > maxRange * maxRange) continue;

            // Smart targeting - consider health, armor, distance
            double score = distance;
            if (smartTarget.get()) {
                score = distance / Math.max(1, player.getHealth() + player.getAbsorptionAmount());
                // Factor in armor
                if (armorCheck.get()) {
                    int armorCount = 0;
                    for (ItemStack stack : player.getArmorItems()) {
                        if (stack.getItem() instanceof ArmorItem) armorCount++;
                    }
                    score *= (1.0 + armorCount * 0.2);
                }
            }

            if (score < bestScore) {
                bestScore = score;
                bestTarget = player;
            }
        }

        return bestTarget;
    }

    private boolean hasCrystals() {
        return InvUtils.find(Items.END_CRYSTAL).found();
    }

    private void switchToCrystals() {
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (crystals.found() && crystals.getHand() == null) {
            InvUtils.swap(crystals.slot(), false);
        }
    }

    private void handlePlacement() {
        if (mc.world == null || mc.player == null || target == null) return;

        long now = System.currentTimeMillis();
        if (now - lastPlaceTime < placeDelay.get()) return;

        // Clear previous placement positions
        placementPositions.clear();

        // Find valid placement positions
        List<BlockPos> validPositions = findValidPlacementPositions();
        if (validPositions.isEmpty()) {
            placeAttempts++;
            return;
        }

        // Determine how many crystals to place based on mode
        int crystalsToPlace = getCrystalsToPlace();
        int actuallyPlaced = 0;

        for (BlockPos pos : validPositions) {
            if (actuallyPlaced >= crystalsToPlace) break;
            if (crystalsPlacedThisTick >= maxCrystals.get()) break;

            if (placeCrystal(pos)) {
                placementPositions.add(pos);
                actuallyPlaced++;
                crystalsPlacedThisTick++;
                lastPlaceTime = now;
                
                // Add delay between multiple placements
                if (actuallyPlaced < crystalsToPlace && multiDelay.get() > 0) {
                    try {
                        Thread.sleep(multiDelay.get() * 50); // Convert ticks to ms
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        if (actuallyPlaced > 0) {
            placeAttempts = 0; // Reset on successful placement
        } else {
            placeAttempts++;
        }
    }

    private List<BlockPos> findValidPlacementPositions() {
        List<BlockPos> positions = new ArrayList<>();
        if (target == null) return positions;

        BlockPos targetPos = target.getBlockPos();
        double range = placeRange.get();
        
        // Generate candidate positions around target
        List<BlockPos> candidates = new ArrayList<>();
        
        switch (placementPattern.get()) {
            case Surrounding -> {
                // Ring around target
                for (int x = -2; x <= 2; x++) {
                    for (int z = -2; z <= 2; z++) {
                        if (x == 0 && z == 0) continue;
                        candidates.add(targetPos.add(x, 0, z));
                        candidates.add(targetPos.add(x, 1, z));
                        candidates.add(targetPos.add(x, -1, z));
                    }
                }
            }
            case Linear -> {
                // Line towards target
                Vec3d direction = target.getPos().subtract(mc.player.getPos()).normalize();
                for (int i = 1; i <= 4; i++) {
                    BlockPos pos = mc.player.getBlockPos().add(
                        (int)(direction.x * i),
                        0,
                        (int)(direction.z * i)
                    );
                    candidates.add(pos);
                    candidates.add(pos.up());
                }
            }
            case Cross -> {
                // Cross pattern around target
                candidates.add(targetPos.add(1, 0, 0));
                candidates.add(targetPos.add(-1, 0, 0));
                candidates.add(targetPos.add(0, 0, 1));
                candidates.add(targetPos.add(0, 0, -1));
                candidates.add(targetPos.add(1, 1, 0));
                candidates.add(targetPos.add(-1, 1, 0));
                candidates.add(targetPos.add(0, 1, 1));
                candidates.add(targetPos.add(0, 1, -1));
            }
            case Diamond -> {
                // Diamond pattern
                candidates.add(targetPos.add(2, 0, 0));
                candidates.add(targetPos.add(-2, 0, 0));
                candidates.add(targetPos.add(0, 0, 2));
                candidates.add(targetPos.add(0, 0, -2));
                candidates.add(targetPos.add(1, 1, 1));
                candidates.add(targetPos.add(-1, 1, 1));
                candidates.add(targetPos.add(1, 1, -1));
                candidates.add(targetPos.add(-1, 1, -1));
            }
            case Optimal -> {
                // Calculate optimal positions based on damage
                for (int x = -3; x <= 3; x++) {
                    for (int y = -1; y <= 2; y++) {
                        for (int z = -3; z <= 3; z++) {
                            if (x == 0 && y == 0 && z == 0) continue;
                            candidates.add(targetPos.add(x, y, z));
                        }
                    }
                }
            }
        }

        // Filter and sort candidates
        for (BlockPos pos : candidates) {
            if (mc.player.squaredDistanceTo(Vec3d.ofCenter(pos)) > range * range) continue;
            if (!isValidPlacementPosition(pos)) continue;
            
            double damage = calculateCrystalDamage(pos, target);
            double selfDamage = calculateCrystalDamage(pos, mc.player);
            
            if (damage < minDamage.get()) continue;
            if (selfDamage > maxSelfDamage.get()) continue;
            if (lethalCheck.get() && selfDamage >= mc.player.getHealth()) continue;
            
            positions.add(pos);
        }

        // Sort by damage potential (higher damage first)
        positions.sort((pos1, pos2) -> {
            double damage1 = calculateCrystalDamage(pos1, target);
            double damage2 = calculateCrystalDamage(pos2, target);
            return Double.compare(damage2, damage1);
        });

        return positions;
    }

    private boolean isValidPlacementPosition(BlockPos pos) {
        if (mc.world == null) return false;

        // Check if base block is valid
        Block baseBlock = mc.world.getBlockState(pos.down()).getBlock();
        if (strictDirection.get() && baseBlock != Blocks.OBSIDIAN && baseBlock != Blocks.BEDROCK) {
            return false;
        }

        // Check if position is clear
        if (!mc.world.getBlockState(pos).isAir()) return false;
        if (!mc.world.getBlockState(pos.up()).isAir()) return false;

        // Check for protected regions
        if (Skylandia.isProtectedRegion(pos)) return false;

        // Check for friend proximity
        if (ignoreFriends.get()) {
            for (PlayerEntity friend : mc.world.getPlayers()) {
                if (Friends.get().isFriend(friend) && 
                    friend.getBlockPos().isWithinDistance(pos, 3)) {
                    return false;
                }
            }
        }

        return true;
    }

    private int getCrystalsToPlace() {
        switch (multiCrystalMode.get()) {
            case Disabled -> { return 1; }
            case Dual -> { return 2; }
            case Triple -> { return 3; }
            case Quad -> { return 4; }
            case Adaptive -> {
                // Adaptive based on target health and situation
                if (target == null) return 1;
                double targetHealth = target.getHealth() + target.getAbsorptionAmount();
                if (targetHealth > 15) return 3;
                if (targetHealth > 10) return 2;
                return 1;
            }
            default -> { return 1; }
        }
    }

    private boolean placeCrystal(BlockPos pos) {
        if (!hasCrystals()) return false;

        // Rotate if enabled
        if (rotate.get()) {
            Vec3d targetVec = Vec3d.ofCenter(pos);
            Rotations.rotate(Rotations.getYaw(targetVec), Rotations.getPitch(targetVec));
        }

        // Place crystal
        BlockHitResult result = new BlockHitResult(
            Vec3d.ofCenter(pos),
            Direction.UP,
            pos.down(),
            false
        );

        boolean success = mc.interactionManager.interactBlock(
            mc.player,
            Hand.MAIN_HAND,
            result
        ).isAccepted();

        if (success) {
            mc.player.swingHand(Hand.MAIN_HAND);
        }

        return success;
    }

    private double calculateCrystalDamage(BlockPos crystalPos, Entity target) {
        if (target == null) return 0;
        
        Vec3d crystalVec = Vec3d.ofCenter(crystalPos).add(0, 1, 0);
        Vec3d targetVec = target.getPos().add(0, target.getHeight() / 2, 0);
        double distance = crystalVec.distanceTo(targetVec);
        
        if (distance > 12) return 0;
        
        // Base damage calculation (similar to actual crystal damage)
        double damage = (1.0 - distance / 12.0) * 12.0;
        
        // Apply armor reduction if enabled and target is player
        if (armorCheck.get() && target instanceof PlayerEntity player) {
            double armorValue = 0;
            for (ItemStack stack : player.getArmorItems()) {
                if (stack.getItem() instanceof ArmorItem armorItem) {
                    // Use a simplified armor calculation since getProtection() might not be available
                    armorValue += 2; // Simplified: each armor piece adds 2 points
                }
            }
            // Simple armor calculation
            double armorReduction = Math.min(20, Math.max(armorValue / 5, armorValue - damage / (2 + armorValue / 25)));
            damage = damage * (1 - armorReduction / 25);
        }
        
        return Math.max(0, damage);
    }

    private void handleBreaking() {
        if (mc.world == null || mc.player == null) return;

        long now = System.currentTimeMillis();
        if (now - lastBreakTime < breakDelay.get()) return;

        // Clear previous breakable crystals
        breakableCrystals.clear();

        double range = breakRange.get();
        int crystalsBroken = 0;
        int maxBreaksPerTick = multiCrystalMode.get() == MultiCrystalMode.Disabled ? 1 : maxCrystals.get();

        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof EndCrystalEntity crystal)) continue;
            if (crystal.isRemoved()) continue;
            if (crystalsBroken >= maxBreaksPerTick) break;

            double distance = mc.player.squaredDistanceTo(crystal);
            if (distance > range * range) continue;

            // Check if crystal is worth breaking
            if (target != null) {
                double damage = calculateCrystalDamage(crystal.getBlockPos(), target);
                double selfDamage = calculateCrystalDamage(crystal.getBlockPos(), mc.player);
                
                // Break if it would damage target significantly or if inhibit mode
                boolean shouldBreak = damage >= minDamage.get() / 2 || // Lower threshold for breaking
                                    (inhibit.get() && damage > 0) ||
                                    selfDamage > maxSelfDamage.get(); // Break dangerous crystals
                
                if (!shouldBreak) continue;
                if (lethalCheck.get() && selfDamage >= mc.player.getHealth()) continue;
            }

            // Check protected regions
            if (Skylandia.isProtectedRegion(crystal.getBlockPos())) continue;

            // Check friend proximity
            if (ignoreFriends.get()) {
                boolean nearFriend = false;
                for (PlayerEntity friend : mc.world.getPlayers()) {
                    if (Friends.get().isFriend(friend) && 
                        friend.getBlockPos().isWithinDistance(crystal.getBlockPos(), 3)) {
                        nearFriend = true;
                        break;
                    }
                }
                if (nearFriend) continue;
            }

            // Break the crystal
            if (breakCrystal(crystal)) {
                breakableCrystals.add(crystal);
                crystalsBroken++;
                lastBreakTime = now;
                
                // Add small delay between multiple breaks
                if (crystalsBroken < maxBreaksPerTick && multiDelay.get() > 0) {
                    try {
                        Thread.sleep(multiDelay.get() * 25); // Shorter delay for breaking
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        if (crystalsBroken > 0) {
            breakAttempts = 0; // Reset on successful break
        } else {
            breakAttempts++;
        }
    }

    private boolean breakCrystal(EndCrystalEntity crystal) {
        // Rotate if enabled
        if (rotate.get()) {
            Vec3d crystalPos = crystal.getPos();
            Rotations.rotate(Rotations.getYaw(crystalPos), Rotations.getPitch(crystalPos));
        }

        // Attack the crystal
        mc.getNetworkHandler().sendPacket(PlayerInteractEntityC2SPacket.attack(crystal, mc.player.isSneaking()));
        mc.getNetworkHandler().sendPacket(new HandSwingC2SPacket(Hand.MAIN_HAND));
        
        return true;
    }
    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;

        // Render placement positions
        if (renderPlacement.get()) {
            for (BlockPos pos : placementPositions) {
                event.renderer.box(pos, placementColor.get(), placementColor.get(), ShapeMode.Both, 0);
                
                // Render damage text if enabled
                if (renderDamage.get() && target != null) {
                    double damage = calculateCrystalDamage(pos, target);
                    String damageText = String.format("%.1f", damage);
                    // Simple text rendering without the text method
                    // event.renderer.text method might not be available, so we'll skip text rendering for now
                }
            }
        }

        // Render breakable crystals
        if (renderBreaking.get()) {
            for (EndCrystalEntity crystal : breakableCrystals) {
                event.renderer.box(
                    crystal.getBoundingBox(),
                    breakingColor.get(),
                    breakingColor.get(),
                    ShapeMode.Lines,
                    0
                );
                
                // Render damage text if enabled
                if (renderDamage.get() && target != null) {
                    double damage = calculateCrystalDamage(crystal.getBlockPos(), target);
                    String damageText = String.format("%.1f", damage);
                    // Simple text rendering without the text method
                    // event.renderer.text method might not be available, so we'll skip text rendering for now
                }
            }
        }
    }

    @EventHandler
    private void onRender2D(Render2DEvent event) {
        if (mc.player == null || mc.world == null) return;
        
        TextRenderer textRenderer = TextRenderer.get();
        int y = 10;
        
        textRenderer.begin(1, false, true);
        
        // Module status
        textRenderer.render(String.format("Boomer [%s]", multiCrystalMode.get().toString()), 
                          10, y, Color.WHITE);
        y += 12;
        
        // Target info
        if (target != null) {
            textRenderer.render(String.format("Target: %s (%.1f HP)", 
                              target.getName().getString(), target.getHealth()), 10, y, Color.GREEN);
            y += 12;
        } else {
            textRenderer.render("Target: None", 10, y, Color.RED);
            y += 12;
        }
        
        // Crystal counts
        textRenderer.render(String.format("Placements: %d | Breakables: %d", 
                          placementPositions.size(), breakableCrystals.size()), 10, y, Color.WHITE);
        y += 12;
        
        // Crystal inventory
        int crystalCount = InvUtils.find(Items.END_CRYSTAL).count();
        textRenderer.render(String.format("Crystals: %d", crystalCount), 10, y, 
                          crystalCount > 0 ? Color.GREEN : Color.RED);
        
        textRenderer.end();
    }

    @Override
    public String getInfoString() {
        if (target == null) return "No Target";
        return String.format("%s (%.1f HP)", target.getName().getString(), target.getHealth());
    }
}