package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.entity.player.InteractBlockEvent;
import meteordevelopment.meteorclient.events.entity.player.InteractEntityEvent;
import meteordevelopment.meteorclient.events.entity.player.InteractItemEvent;
import meteordevelopment.meteorclient.events.game.GameLeftEvent;
import meteordevelopment.meteorclient.events.game.OpenScreenEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.render.NametagUtils;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.EventPriority;
import net.minecraft.block.Block;
import net.minecraft.client.gui.screen.DisconnectedScreen;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerInventory;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.c2s.play.*;
import net.minecraft.network.packet.s2c.play.*;
import net.minecraft.network.packet.c2s.common.KeepAliveC2SPacket;
import net.minecraft.screen.slot.SlotActionType;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import org.joml.Vector3d;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

public class duprexion extends Module {

    // === ENUMS ===

    public enum RecordingMode {
        RECORD("Record", "Record all game actions and events", "§b"),
        PLAYBACK("Playback", "Replay recorded sessions", "§a"),
        ANALYSIS("Analysis", "Analyze recordings for dupe patterns", "§e"),
        LIVE_MONITOR("Live Monitor", "Real-time dupe detection", "§d");

        public final String title;
        public final String description;
        public final String color;

        RecordingMode(String title, String description, String color) {
            this.title = title;
            this.description = description;
            this.color = color;
        }
    }

    public enum PlaybackSpeed {
        QUARTER(0.25, "0.25x"),
        HALF(0.5, "0.5x"),
        NORMAL(1.0, "1.0x"),
        DOUBLE(2.0, "2.0x"),
        QUAD(4.0, "4.0x"),
        FRAME_BY_FRAME(0.0, "Frame");

        public final double multiplier;
        public final String display;

        PlaybackSpeed(double multiplier, String display) {
            this.multiplier = multiplier;
            this.display = display;
        }
    }

    public enum EventType {
        PACKET_SEND("Packet Send"),
        PACKET_RECEIVE("Packet Receive"),
        PLAYER_ACTION("Player Action"),
        INVENTORY_CHANGE("Inventory Change"),
        BLOCK_INTERACTION("Block Interaction"),
        ENTITY_INTERACTION("Entity Interaction"),
        ITEM_USE("Item Use"),
        POSITION_CHANGE("Position Change"),
        SCREEN_OPEN("Screen Open"),
        TICK_EVENT("Tick Event");

        public final String display;

        EventType(String display) {
            this.display = display;
        }
    }

    // === SETTING GROUPS ===
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgRecording = settings.createGroup("📹 Recording");
    private final SettingGroup sgPlayback = settings.createGroup("▶️ Playback");
    private final SettingGroup sgAnalysis = settings.createGroup("🔍 Analysis");
    private final SettingGroup sgFilters = settings.createGroup("🔧 Filters");
    private final SettingGroup sgRender = settings.createGroup("🎨 Render");
    private final SettingGroup sgDebug = settings.createGroup("🐛 Debug");

    // === GENERAL SETTINGS ===
    private final Setting<RecordingMode> mode = sgGeneral.add(new EnumSetting.Builder<RecordingMode>()
        .name("mode")
        .description("Operating mode for the duplication detection system")
        .defaultValue(RecordingMode.RECORD)
        .onChanged(this::onModeChange)
        .build()
    );

    private final Setting<String> sessionName = sgGeneral.add(new StringSetting.Builder()
        .name("session-name")
        .description("Name for the current recording session")
        .defaultValue("session_" + System.currentTimeMillis())
        .build()
    );

    private final Setting<Boolean> autoSave = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-save")
        .description("Automatically save recordings when stopping")
        .defaultValue(true)
        .build()
    );

    // === RECORDING SETTINGS ===
    private final Setting<Boolean> recordPackets = sgRecording.add(new BoolSetting.Builder()
        .name("record-packets")
        .description("Record all network packets")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> recordPlayerActions = sgRecording.add(new BoolSetting.Builder()
        .name("record-player-actions")
        .description("Record player interactions and movements")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> recordInventoryChanges = sgRecording.add(new BoolSetting.Builder()
        .name("record-inventory")
        .description("Record inventory state changes")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> recordBlockInteractions = sgRecording.add(new BoolSetting.Builder()
        .name("record-blocks")
        .description("Record block placement and breaking")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> recordEntityInteractions = sgRecording.add(new BoolSetting.Builder()
        .name("record-entities")
        .description("Record entity interactions")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> maxRecordingTime = sgRecording.add(new IntSetting.Builder()
        .name("max-recording-time")
        .description("Maximum recording time in seconds (0 = unlimited)")
        .defaultValue(300)
        .min(0)
        .sliderMax(3600)
        .build()
    );

    // === PLAYBACK SETTINGS ===
    private final Setting<PlaybackSpeed> playbackSpeed = sgPlayback.add(new EnumSetting.Builder<PlaybackSpeed>()
        .name("playback-speed")
        .description("Speed of playback")
        .defaultValue(PlaybackSpeed.NORMAL)
        .build()
    );

    private final Setting<Boolean> loopPlayback = sgPlayback.add(new BoolSetting.Builder()
        .name("loop-playback")
        .description("Loop playback when it reaches the end")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> syncPlayback = sgPlayback.add(new BoolSetting.Builder()
        .name("sync-playback")
        .description("Synchronize playback timing with original recording")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> outOfSyncMode = sgPlayback.add(new BoolSetting.Builder()
        .name("out-of-sync-mode")
        .description("Enable out-of-sync playback for timing exploit analysis")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> syncOffset = sgPlayback.add(new IntSetting.Builder()
        .name("sync-offset")
        .description("Timing offset in milliseconds for out-of-sync mode")
        .defaultValue(0)
        .min(-1000)
        .max(1000)
        .visible(outOfSyncMode::get)
        .build()
    );

    // === ANALYSIS SETTINGS ===
    private final Setting<Boolean> detectDupePatterns = sgAnalysis.add(new BoolSetting.Builder()
        .name("detect-dupe-patterns")
        .description("Automatically detect potential duplication patterns")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> analyzeTimingAnomalies = sgAnalysis.add(new BoolSetting.Builder()
        .name("analyze-timing")
        .description("Analyze timing anomalies that may indicate exploits")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> suspiciousThreshold = sgAnalysis.add(new IntSetting.Builder()
        .name("suspicious-threshold")
        .description("Threshold for flagging suspicious patterns (lower = more sensitive)")
        .defaultValue(3)
        .min(1)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> highlightAnomalies = sgAnalysis.add(new BoolSetting.Builder()
        .name("highlight-anomalies")
        .description("Highlight detected anomalies during playback")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> autoDisable = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-disable")
        .description("Automatically disable after completing sequence")
        .defaultValue(false)
        .build()
    );

    private final Setting<List<Item>> targetItems = sgGeneral.add(new ItemListSetting.Builder()
        .name("target-items")
        .description("Items to test in discovery mode")
        .defaultValue(List.of(Items.TRIDENT, Items.SHULKER_BOX))
        .build()
    );

    // Timing Settings
    private final Setting<Integer> baseDelay = sgRecording.add(new IntSetting.Builder()
        .name("base-delay")
        .description("Base delay between actions (ms)")
        .defaultValue(500)
        .range(50, 2000)
        .sliderRange(50, 2000)
        .build()
    );

    private final Setting<Integer> variance = sgRecording.add(new IntSetting.Builder()
        .name("variance")
        .description("Random delay variance (ms)")
        .defaultValue(50)
        .range(0, 500)
        .build()
    );

    private final Setting<Integer> maxAttempts = sgRecording.add(new IntSetting.Builder()
        .name("max-attempts")
        .description("Maximum attempts before auto-disable (0 = infinite)")
        .defaultValue(0)
        .min(0)
        .build()
    );

    // Packet Settings
    private final Setting<Boolean> cancelOutgoing = sgFilters.add(new BoolSetting.Builder()
        .name("cancel-outgoing")
        .description("Cancel outgoing packets during sequence")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> cancelIncoming = sgFilters.add(new BoolSetting.Builder()
        .name("cancel-incoming")
        .description("Cancel incoming packets during sequence")
        .defaultValue(false)
        .build()
    );

    // Trident Settings
    private final Setting<Boolean> swapBack = sgRecording.add(new BoolSetting.Builder()
        .name("swap-back")
        .description("Swap trident back after sequence")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> dropDuped = sgRecording.add(new BoolSetting.Builder()
        .name("drop-duped")
        .description("Drop duped tridents")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> durabilityCheck = sgRecording.add(new BoolSetting.Builder()
        .name("durability-check")
        .description("Only use tridents above durability threshold")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> minDurability = sgRecording.add(new IntSetting.Builder()
        .name("min-durability")
        .description("Minimum trident durability %")
        .defaultValue(20)
        .range(1, 100)
        .visible(durabilityCheck::get)
        .build()
    );

    // Display Settings
    private final Setting<Boolean> showHUD = sgRender.add(new BoolSetting.Builder()
        .name("show-hud")
        .description("Show info in HUD")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> showToasts = sgRender.add(new BoolSetting.Builder()
        .name("show-toasts")
        .description("Show status messages")
        .defaultValue(true)
        .build()
    );

    // Debug Settings
    private final Setting<Boolean> debugLog = sgDebug.add(new BoolSetting.Builder()
        .name("debug-log")
        .description("Log detailed debug info")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> catchExceptions = sgDebug.add(new BoolSetting.Builder()
        .name("catch-exceptions")
        .description("Prevent crashes from packet errors")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> verboseOutput = sgDebug.add(new BoolSetting.Builder()
        .name("verbose-output")
        .description("Show detailed output messages")
        .defaultValue(false)
        .build()
    );

    // Additional Render Settings
    private final Setting<Boolean> renderTimeline = sgRender.add(new BoolSetting.Builder()
        .name("render-timeline")
        .description("Render playback timeline and progress")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> playbackColor = sgRender.add(new ColorSetting.Builder()
        .name("playback-color")
        .description("Color for playback timeline text")
        .defaultValue(new SettingColor(255, 255, 255, 255))
        .build()
    );

    // Additional Filter Settings
    private final Setting<Boolean> filterPlayerMovement = sgFilters.add(new BoolSetting.Builder()
        .name("filter-player-movement")
        .description("Filter out player movement packets")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> filterHeartbeat = sgFilters.add(new BoolSetting.Builder()
        .name("filter-heartbeat")
        .description("Filter out heartbeat/keepalive packets")
        .defaultValue(true)
        .build()
    );

    private final Setting<List<String>> packetFilter = sgFilters.add(new StringListSetting.Builder()
        .name("packet-filter")
        .description("Only record these packet types (empty = all)")
        .defaultValue(List.of())
        .build()
    );

    // Additional Analysis Settings
    private final Setting<Boolean> exportAnalysis = sgAnalysis.add(new BoolSetting.Builder()
        .name("export-analysis")
        .description("Export detailed analysis reports to files")
        .defaultValue(false)
        .build()
    );

    // === STATE VARIABLES ===
    private final List<RecordedEvent> currentSession = new CopyOnWriteArrayList<>();
    private final Map<String, List<RecordedEvent>> savedSessions = new ConcurrentHashMap<>();
    private final List<DupePattern> detectedPatterns = new ArrayList<>();
    private final List<Anomaly> detectedAnomalies = new ArrayList<>();

    private boolean isRecording = false;
    private boolean isPlayingBack = false;
    private boolean isPaused = false;
    private long recordingStartTime = 0;
    private long playbackStartTime = 0;
    private int currentPlaybackIndex = 0;
    private String currentSessionName = "";
    private int frameCounter = 0;

    // Analysis state
    private final Map<String, Integer> packetCounts = new ConcurrentHashMap<>();
    private final Map<String, Long> lastPacketTimes = new ConcurrentHashMap<>();
    private final List<InventorySnapshot> inventoryHistory = new ArrayList<>();

    public duprexion() {
        super(Skylandia.Automation, "duprexion", "Comprehensive recording and playback system for identifying item duplication exploits");
    }

    // === DATA CLASSES ===

    public static class RecordedEvent {
        public final EventType type;
        public final long timestamp;
        public final long relativeTime;
        public final Map<String, Object> data;
        public final Vec3d playerPosition;
        public final float playerYaw;
        public final float playerPitch;
        public final int tickCount;

        public RecordedEvent(EventType type, long timestamp, long relativeTime, Map<String, Object> data,
                           Vec3d playerPosition, float playerYaw, float playerPitch, int tickCount) {
            this.type = type;
            this.timestamp = timestamp;
            this.relativeTime = relativeTime;
            this.data = new HashMap<>(data);
            this.playerPosition = playerPosition;
            this.playerYaw = playerYaw;
            this.playerPitch = playerPitch;
            this.tickCount = tickCount;
        }
    }

    public static class DupePattern {
        public final String name;
        public final List<RecordedEvent> events;
        public final double confidence;
        public final String description;

        public DupePattern(String name, List<RecordedEvent> events, double confidence, String description) {
            this.name = name;
            this.events = new ArrayList<>(events);
            this.confidence = confidence;
            this.description = description;
        }
    }

    public static class Anomaly {
        public final String type;
        public final long timestamp;
        public final String description;
        public final double severity;
        public final RecordedEvent relatedEvent;

        public Anomaly(String type, long timestamp, String description, double severity, RecordedEvent relatedEvent) {
            this.type = type;
            this.timestamp = timestamp;
            this.description = description;
            this.severity = severity;
            this.relatedEvent = relatedEvent;
        }
    }

    public static class InventorySnapshot {
        public final long timestamp;
        public final Map<Integer, ItemStack> slots;
        public final ItemStack cursorStack;

        public InventorySnapshot(long timestamp, PlayerInventory inventory, ItemStack cursorStack) {
            this.timestamp = timestamp;
            this.slots = new HashMap<>();
            this.cursorStack = cursorStack != null ? cursorStack.copy() : ItemStack.EMPTY;

            for (int i = 0; i < inventory.size(); i++) {
                ItemStack stack = inventory.getStack(i);
                if (!stack.isEmpty()) {
                    this.slots.put(i, stack.copy());
                }
            }
        }
    }

    @Override
    public void onActivate() {
        if (mc.player == null) return;

        resetState();

        RecordingMode currentMode = mode.get();
        info(currentMode.color + "Activated: " + currentMode.title);
        info("§7" + currentMode.description);

        switch (currentMode) {
            case RECORD -> startRecording();
            case PLAYBACK -> startPlayback();
            case ANALYSIS -> startAnalysis();
            case LIVE_MONITOR -> startLiveMonitoring();
            default -> info("§cSelected mode not implemented");
        }
    }

    @Override
    public void onDeactivate() {
        if (isRecording) {
            stopRecording();
        }
        if (isPlayingBack) {
            stopPlayback();
        }

        // Show session summary
        if (!currentSession.isEmpty()) {
            info(String.format("§7Session Summary: §f%d §7events recorded over §f%.1fs",
                currentSession.size(),
                (System.currentTimeMillis() - recordingStartTime) / 1000.0));
        }

        if (!detectedAnomalies.isEmpty()) {
            info(String.format("§e%d §7anomalies detected", detectedAnomalies.size()));
        }
    }

    private void onModeChange(RecordingMode newMode) {
        if (isActive()) {
            toggle();
            mode.set(newMode);
            toggle();
        }
    }

    private void resetState() {
        isRecording = false;
        isPlayingBack = false;
        isPaused = false;
        currentPlaybackIndex = 0;
        frameCounter = 0;
        recordingStartTime = 0;
        playbackStartTime = 0;
        currentSessionName = sessionName.get();

        packetCounts.clear();
        lastPacketTimes.clear();
        detectedAnomalies.clear();
    }

    // === RECORDING METHODS ===

    private void startRecording() {
        currentSession.clear();
        recordingStartTime = System.currentTimeMillis();
        isRecording = true;
        frameCounter = 0;

        info("§b📹 Recording started: " + currentSessionName);
        if (maxRecordingTime.get() > 0) {
            info("§7Max recording time: " + maxRecordingTime.get() + "s");
        }

        // Take initial inventory snapshot
        if (recordInventoryChanges.get() && mc.player != null) {
            takeInventorySnapshot();
        }
    }

    private void stopRecording() {
        if (!isRecording) return;

        isRecording = false;
        long duration = System.currentTimeMillis() - recordingStartTime;

        info(String.format("§a📹 Recording stopped: §f%d §aevents in §f%.1fs",
            currentSession.size(), duration / 1000.0));

        if (autoSave.get()) {
            saveCurrentSession();
        }

        if (detectDupePatterns.get()) {
            analyzeSession(currentSession);
        }
    }

    private void saveCurrentSession() {
        if (currentSession.isEmpty()) {
            info("§cNo events to save");
            return;
        }

        try {
            Path recordingsDir = Paths.get("duprexion_recordings");
            Files.createDirectories(recordingsDir);

            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String filename = currentSessionName + "_" + timestamp + ".json";
            Path filePath = recordingsDir.resolve(filename);

            // Save session data
            Map<String, Object> sessionData = new HashMap<>();
            sessionData.put("name", currentSessionName);
            sessionData.put("timestamp", timestamp);
            sessionData.put("duration", System.currentTimeMillis() - recordingStartTime);
            sessionData.put("eventCount", currentSession.size());
            sessionData.put("events", serializeEvents(currentSession));
            sessionData.put("anomalies", serializeAnomalies(detectedAnomalies));

            // Write to file (simplified JSON-like format)
            try (PrintWriter writer = new PrintWriter(Files.newBufferedWriter(filePath))) {
                writeSessionData(writer, sessionData);
            }

            // Store in memory
            savedSessions.put(currentSessionName, new ArrayList<>(currentSession));

            info("§a💾 Session saved: §f" + filename);

        } catch (IOException e) {
            error("§cFailed to save session: " + e.getMessage());
            if (debugLog.get()) {
                e.printStackTrace();
            }
        }
    }

    private void recordEvent(EventType type, Map<String, Object> data) {
        if (!isRecording) return;

        long currentTime = System.currentTimeMillis();
        long relativeTime = currentTime - recordingStartTime;

        Vec3d playerPos = mc.player != null ? mc.player.getPos() : Vec3d.ZERO;
        float yaw = mc.player != null ? mc.player.getYaw() : 0;
        float pitch = mc.player != null ? mc.player.getPitch() : 0;

        RecordedEvent event = new RecordedEvent(
            type, currentTime, relativeTime, data,
            playerPos, yaw, pitch, frameCounter++
        );

        currentSession.add(event);

        if (verboseOutput.get()) {
            info(String.format("§7[%dms] %s", relativeTime, type.display));
        }

        // Check for real-time anomalies
        if (mode.get() == RecordingMode.LIVE_MONITOR) {
            checkForAnomalies(event);
        }

        // Auto-stop if max time reached
        if (maxRecordingTime.get() > 0 && relativeTime > maxRecordingTime.get() * 1000) {
            info("§7Max recording time reached, stopping...");
            stopRecording();
        }
    }

    // === PLAYBACK METHODS ===

    private void startPlayback() {
        if (savedSessions.isEmpty()) {
            error("§cNo saved sessions available for playback");
            return;
        }

        // Use the most recent session or specified session
        String sessionToPlay = currentSessionName;
        if (!savedSessions.containsKey(sessionToPlay)) {
            sessionToPlay = savedSessions.keySet().iterator().next();
        }

        List<RecordedEvent> sessionEvents = savedSessions.get(sessionToPlay);
        if (sessionEvents.isEmpty()) {
            error("§cSelected session is empty");
            return;
        }

        isPlayingBack = true;
        isPaused = false;
        currentPlaybackIndex = 0;
        playbackStartTime = System.currentTimeMillis();

        info("§a▶️ Starting playback: " + sessionToPlay);
        info("§7Events: " + sessionEvents.size() + " | Speed: " + playbackSpeed.get().display);
    }

    private void stopPlayback() {
        if (!isPlayingBack) return;

        isPlayingBack = false;
        isPaused = false;

        info("§a⏹️ Playback stopped");
    }

    private void pausePlayback() {
        if (!isPlayingBack) return;

        isPaused = !isPaused;
        info(isPaused ? "§e⏸️ Playback paused" : "§a▶️ Playback resumed");
    }

    private void stepFrame() {
        if (!isPlayingBack || playbackSpeed.get() != PlaybackSpeed.FRAME_BY_FRAME) return;

        String sessionToPlay = currentSessionName;
        if (!savedSessions.containsKey(sessionToPlay)) {
            sessionToPlay = savedSessions.keySet().iterator().next();
        }

        List<RecordedEvent> sessionEvents = savedSessions.get(sessionToPlay);
        if (currentPlaybackIndex < sessionEvents.size()) {
            processPlaybackEvent(sessionEvents.get(currentPlaybackIndex));
            currentPlaybackIndex++;

            info(String.format("§7Frame %d/%d", currentPlaybackIndex, sessionEvents.size()));
        }
    }

    // === ANALYSIS METHODS ===

    private void startAnalysis() {
        if (savedSessions.isEmpty()) {
            error("§cNo sessions available for analysis");
            return;
        }

        info("§e🔍 Starting analysis of saved sessions...");

        for (Map.Entry<String, List<RecordedEvent>> entry : savedSessions.entrySet()) {
            analyzeSession(entry.getValue());
        }

        generateAnalysisReport();
    }

    private void startLiveMonitoring() {
        info("§d👁️ Live monitoring started - watching for dupe patterns...");
        detectedAnomalies.clear();
    }

    private void analyzeSession(List<RecordedEvent> events) {
        if (events.isEmpty()) return;

        info("§7Analyzing session with " + events.size() + " events...");

        // Detect timing anomalies
        if (analyzeTimingAnomalies.get()) {
            detectTimingAnomalies(events);
        }

        // Detect known dupe patterns
        if (detectDupePatterns.get()) {
            detectKnownDupePatterns(events);
        }

        // Analyze inventory changes
        analyzeInventoryPatterns(events);

        // Analyze packet sequences
        analyzePacketSequences(events);
    }

    private void detectTimingAnomalies(List<RecordedEvent> events) {
        for (int i = 1; i < events.size(); i++) {
            RecordedEvent prev = events.get(i - 1);
            RecordedEvent curr = events.get(i);

            long timeDiff = curr.relativeTime - prev.relativeTime;

            // Flag suspiciously fast sequences
            if (timeDiff < 10 && curr.type == EventType.PACKET_SEND) {
                addAnomaly("FAST_SEQUENCE", curr.timestamp,
                    "Suspiciously fast packet sequence: " + timeDiff + "ms",
                    0.7, curr);
            }

            // Flag identical timing patterns
            if (timeDiff == 0 && i > 1) {
                RecordedEvent prevPrev = events.get(i - 2);
                if (curr.relativeTime - prevPrev.relativeTime == prev.relativeTime - prevPrev.relativeTime) {
                    addAnomaly("IDENTICAL_TIMING", curr.timestamp,
                        "Identical timing pattern detected",
                        0.8, curr);
                }
            }
        }
    }

    private void detectKnownDupePatterns(List<RecordedEvent> events) {
        // Pattern 1: Rapid inventory manipulation
        detectRapidInventoryPattern(events);

        // Pattern 2: Trident dupe pattern
        detectTridentDupePattern(events);

        // Pattern 3: Item frame exploit pattern
        detectItemFramePattern(events);

        // Pattern 4: Drop timing manipulation
        detectDropTimingPattern(events);
    }

    private void detectRapidInventoryPattern(List<RecordedEvent> events) {
        int rapidClicks = 0;
        long windowStart = 0;

        for (RecordedEvent event : events) {
            if (event.type == EventType.PLAYER_ACTION &&
                event.data.containsKey("action") &&
                event.data.get("action").toString().contains("CLICK")) {

                if (windowStart == 0) {
                    windowStart = event.relativeTime;
                    rapidClicks = 1;
                } else if (event.relativeTime - windowStart < 1000) { // 1 second window
                    rapidClicks++;
                } else {
                    windowStart = event.relativeTime;
                    rapidClicks = 1;
                }

                if (rapidClicks >= suspiciousThreshold.get()) {
                    addAnomaly("RAPID_INVENTORY", event.timestamp,
                        "Rapid inventory manipulation detected: " + rapidClicks + " clicks in 1s",
                        0.9, event);
                }
            }
        }
    }

    private void detectTridentDupePattern(List<RecordedEvent> events) {
        // Look for specific trident dupe sequence: right-click -> swap -> swap -> release
        for (int i = 0; i < events.size() - 3; i++) {
            RecordedEvent e1 = events.get(i);
            RecordedEvent e2 = events.get(i + 1);
            RecordedEvent e3 = events.get(i + 2);
            RecordedEvent e4 = events.get(i + 3);

            if (isRightClickEvent(e1) && isSwapEvent(e2) && isSwapEvent(e3) && isReleaseEvent(e4)) {
                long totalTime = e4.relativeTime - e1.relativeTime;
                if (totalTime < 500) { // Suspiciously fast
                    addAnomaly("TRIDENT_DUPE", e1.timestamp,
                        "Potential trident dupe pattern detected in " + totalTime + "ms",
                        0.95, e1);
                }
            }
        }
    }

    private void analyzeInventoryPatterns(List<RecordedEvent> events) {
        // Track inventory changes for impossible item increases
        Map<String, Integer> itemCounts = new HashMap<>();

        for (RecordedEvent event : events) {
            if (event.type == EventType.INVENTORY_CHANGE && event.data.containsKey("item")) {
                String item = event.data.get("item").toString();
                int count = (Integer) event.data.getOrDefault("count", 0);

                int previousCount = itemCounts.getOrDefault(item, 0);
                if (count > previousCount + 1) { // Impossible increase
                    addAnomaly("ITEM_INCREASE", event.timestamp,
                        "Impossible item count increase: " + item + " +" + (count - previousCount),
                        1.0, event);
                }

                itemCounts.put(item, count);
            }
        }
    }

    // === HELPER METHODS ===

    private void analyzePacketSequences(List<RecordedEvent> events) {
        Map<String, Integer> packetSequence = new HashMap<>();

        for (RecordedEvent event : events) {
            if (event.type == EventType.PACKET_SEND && event.data.containsKey("packetType")) {
                String packetType = event.data.get("packetType").toString();
                packetSequence.put(packetType, packetSequence.getOrDefault(packetType, 0) + 1);
            }
        }

        // Flag unusual packet patterns
        for (Map.Entry<String, Integer> entry : packetSequence.entrySet()) {
            if (entry.getValue() > 100) { // Excessive packet count
                addAnomaly("PACKET_SPAM", System.currentTimeMillis(),
                    "Excessive " + entry.getKey() + " packets: " + entry.getValue(),
                    0.8, null);
            }
        }
    }

    private void addAnomaly(String type, long timestamp, String description, double severity, RecordedEvent relatedEvent) {
        Anomaly anomaly = new Anomaly(type, timestamp, description, severity, relatedEvent);
        detectedAnomalies.add(anomaly);

        if (verboseOutput.get()) {
            info(String.format("§e⚠️ Anomaly: %s (%.1f)", description, severity));
        }
    }

    private void checkForAnomalies(RecordedEvent event) {
        // Real-time anomaly detection during live monitoring
        if (event.type == EventType.PACKET_SEND) {
            String packetType = event.data.getOrDefault("packetType", "Unknown").toString();

            // Track packet frequency
            packetCounts.put(packetType, packetCounts.getOrDefault(packetType, 0) + 1);
            Long lastTime = lastPacketTimes.get(packetType);

            if (lastTime != null && event.timestamp - lastTime < 10) {
                addAnomaly("RAPID_PACKETS", event.timestamp,
                    "Rapid " + packetType + " packets detected",
                    0.7, event);
            }

            lastPacketTimes.put(packetType, event.timestamp);
        }
    }

    private void takeInventorySnapshot() {
        if (mc.player == null) return;

        ItemStack cursorStack = mc.player.currentScreenHandler != null ?
            mc.player.currentScreenHandler.getCursorStack() : ItemStack.EMPTY;

        InventorySnapshot snapshot = new InventorySnapshot(
            System.currentTimeMillis(),
            mc.player.getInventory(),
            cursorStack
        );

        inventoryHistory.add(snapshot);

        // Record inventory change event
        Map<String, Object> data = new HashMap<>();
        data.put("snapshotId", inventoryHistory.size() - 1);
        data.put("totalItems", snapshot.slots.size());
        recordEvent(EventType.INVENTORY_CHANGE, data);
    }

    private void processPlaybackEvent(RecordedEvent event) {
        if (!isPlayingBack || isPaused) return;

        // Apply timing based on playback speed
        PlaybackSpeed speed = playbackSpeed.get();
        if (speed == PlaybackSpeed.FRAME_BY_FRAME) return; // Handled separately

        long expectedTime = (long) (event.relativeTime * speed.multiplier);
        long actualTime = System.currentTimeMillis() - playbackStartTime;

        if (syncPlayback.get() && actualTime < expectedTime) {
            // Wait for proper timing
            return;
        }

        // Highlight anomalies during playback
        if (highlightAnomalies.get()) {
            for (Anomaly anomaly : detectedAnomalies) {
                if (anomaly.relatedEvent == event) {
                    info("§e⚠️ Anomaly: " + anomaly.description);
                }
            }
        }

        if (verboseOutput.get()) {
            info(String.format("§7[%dms] %s", event.relativeTime, event.type.display));
        }
    }

    // === UTILITY METHODS ===

    private void generateAnalysisReport() {
        if (detectedAnomalies.isEmpty()) {
            info("§a✅ No anomalies detected in analyzed sessions");
            return;
        }

        info("§e📊 Analysis Report:");
        info("§7Total anomalies: §f" + detectedAnomalies.size());

        Map<String, Long> anomalyTypes = detectedAnomalies.stream()
            .collect(Collectors.groupingBy(a -> a.type, Collectors.counting()));

        for (Map.Entry<String, Long> entry : anomalyTypes.entrySet()) {
            info(String.format("§7- %s: §f%d", entry.getKey(), entry.getValue()));
        }

        // Export detailed report if enabled
        if (exportAnalysis.get()) {
            exportAnalysisReport();
        }
    }

    private void exportAnalysisReport() {
        try {
            Path reportsDir = Paths.get("duprexion_reports");
            Files.createDirectories(reportsDir);

            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            Path reportPath = reportsDir.resolve("analysis_" + timestamp + ".txt");

            try (PrintWriter writer = new PrintWriter(Files.newBufferedWriter(reportPath))) {
                writer.println("=== DUPREXION ANALYSIS REPORT ===");
                writer.println("Generated: " + timestamp);
                writer.println("Total Sessions: " + savedSessions.size());
                writer.println("Total Anomalies: " + detectedAnomalies.size());
                writer.println();

                for (Anomaly anomaly : detectedAnomalies) {
                    writer.printf("[%s] %s (Severity: %.2f)%n",
                        anomaly.type, anomaly.description, anomaly.severity);
                }
            }

            info("§a📄 Analysis report exported: " + reportPath.getFileName());

        } catch (IOException e) {
            error("§cFailed to export analysis report: " + e.getMessage());
        }
    }

    private List<Map<String, Object>> serializeEvents(List<RecordedEvent> events) {
        return events.stream().map(event -> {
            Map<String, Object> serialized = new HashMap<>();
            serialized.put("type", event.type.name());
            serialized.put("timestamp", event.timestamp);
            serialized.put("relativeTime", event.relativeTime);
            serialized.put("data", event.data);
            serialized.put("position", Map.of("x", event.playerPosition.x, "y", event.playerPosition.y, "z", event.playerPosition.z));
            serialized.put("rotation", Map.of("yaw", event.playerYaw, "pitch", event.playerPitch));
            serialized.put("tick", event.tickCount);
            return serialized;
        }).collect(Collectors.toList());
    }

    private List<Map<String, Object>> serializeAnomalies(List<Anomaly> anomalies) {
        return anomalies.stream().map(anomaly -> {
            Map<String, Object> serialized = new HashMap<>();
            serialized.put("type", anomaly.type);
            serialized.put("timestamp", anomaly.timestamp);
            serialized.put("description", anomaly.description);
            serialized.put("severity", anomaly.severity);
            return serialized;
        }).collect(Collectors.toList());
    }

    private void writeSessionData(PrintWriter writer, Map<String, Object> sessionData) {
        writer.println("{");
        for (Map.Entry<String, Object> entry : sessionData.entrySet()) {
            writer.printf("  \"%s\": %s,%n", entry.getKey(), formatValue(entry.getValue()));
        }
        writer.println("}");
    }

    private String formatValue(Object value) {
        if (value instanceof String) {
            return "\"" + value + "\"";
        } else if (value instanceof List) {
            return "[...]"; // Simplified for brevity
        } else {
            return value.toString();
        }
    }

    // Pattern detection helpers
    private boolean isRightClickEvent(RecordedEvent event) {
        return event.type == EventType.PLAYER_ACTION &&
               event.data.containsKey("action") &&
               event.data.get("action").toString().contains("RIGHT_CLICK");
    }

    private boolean isSwapEvent(RecordedEvent event) {
        return event.type == EventType.PLAYER_ACTION &&
               event.data.containsKey("action") &&
               event.data.get("action").toString().contains("SWAP");
    }

    private boolean isReleaseEvent(RecordedEvent event) {
        return event.type == EventType.PLAYER_ACTION &&
               event.data.containsKey("action") &&
               event.data.get("action").toString().contains("RELEASE");
    }

    private void detectItemFramePattern(List<RecordedEvent> events) {
        // Look for rapid item frame interactions
        for (RecordedEvent event : events) {
            if (event.type == EventType.ENTITY_INTERACTION &&
                event.data.containsKey("entityType") &&
                event.data.get("entityType").toString().contains("ITEM_FRAME")) {

                addAnomaly("ITEM_FRAME_EXPLOIT", event.timestamp,
                    "Item frame interaction detected", 0.6, event);
            }
        }
    }

    private void detectDropTimingPattern(List<RecordedEvent> events) {
        // Look for precise drop timing patterns
        List<Long> dropTimes = new ArrayList<>();

        for (RecordedEvent event : events) {
            if (event.type == EventType.PLAYER_ACTION &&
                event.data.containsKey("action") &&
                event.data.get("action").toString().contains("DROP")) {
                dropTimes.add(event.relativeTime);
            }
        }

        // Check for suspiciously regular timing
        if (dropTimes.size() >= 3) {
            for (int i = 2; i < dropTimes.size(); i++) {
                long interval1 = dropTimes.get(i-1) - dropTimes.get(i-2);
                long interval2 = dropTimes.get(i) - dropTimes.get(i-1);

                if (Math.abs(interval1 - interval2) < 5) { // Very precise timing
                    addAnomaly("DROP_TIMING", dropTimes.get(i),
                        "Precise drop timing pattern detected", 0.8, null);
                }
            }
        }
    }

    // === EVENT HANDLERS ===

    @EventHandler(priority = EventPriority.HIGHEST)
    private void onSendPacket(PacketEvent.Send event) {
        if (!recordPackets.get() && !isRecording) return;

        // Filter packets based on settings
        String packetType = event.packet.getClass().getSimpleName();

        if (filterPlayerMovement.get() && event.packet instanceof PlayerMoveC2SPacket) {
            return;
        }

        if (filterHeartbeat.get() && event.packet instanceof KeepAliveC2SPacket) {
            return;
        }

        if (!packetFilter.get().isEmpty() && !packetFilter.get().contains(packetType)) {
            return;
        }

        // Record packet event
        Map<String, Object> data = new HashMap<>();
        data.put("packetType", packetType);
        data.put("direction", "SEND");

        if (event.packet instanceof ClickSlotC2SPacket clickPacket) {
            data.put("slot", clickPacket.getSlot());
            data.put("actionType", clickPacket.getActionType().name());
        }

        if (event.packet instanceof PlayerActionC2SPacket actionPacket) {
            data.put("action", actionPacket.getAction().name());
            data.put("pos", actionPacket.getPos().toString());
        }

        recordEvent(EventType.PACKET_SEND, data);
    }

    @EventHandler
    private void onReceivePacket(PacketEvent.Receive event) {
        if (!recordPackets.get() && !isRecording) return;

        String packetType = event.packet.getClass().getSimpleName();

        // Record packet event
        Map<String, Object> data = new HashMap<>();
        data.put("packetType", packetType);
        data.put("direction", "RECEIVE");

        if (event.packet instanceof InventoryS2CPacket inventoryPacket) {
            data.put("syncId", inventoryPacket.getSyncId());
        }

        recordEvent(EventType.PACKET_RECEIVE, data);
    }

    @EventHandler
    private void onInteractItem(InteractItemEvent event) {
        if (!recordPlayerActions.get()) return;

        ItemStack itemStack = mc.player.getStackInHand(event.hand);

        Map<String, Object> data = new HashMap<>();
        data.put("action", "INTERACT_ITEM");
        data.put("item", itemStack.getItem().toString());
        data.put("hand", event.hand.name());

        recordEvent(EventType.ITEM_USE, data);
    }

    @EventHandler
    private void onInteractBlock(InteractBlockEvent event) {
        if (!recordBlockInteractions.get()) return;

        Map<String, Object> data = new HashMap<>();
        data.put("action", "INTERACT_BLOCK");
        data.put("pos", event.result.getBlockPos().toString());
        data.put("block", mc.world.getBlockState(event.result.getBlockPos()).getBlock().toString());
        data.put("hand", event.hand.name());

        recordEvent(EventType.BLOCK_INTERACTION, data);
    }

    @EventHandler
    private void onInteractEntity(InteractEntityEvent event) {
        if (!recordEntityInteractions.get()) return;

        Map<String, Object> data = new HashMap<>();
        data.put("action", "INTERACT_ENTITY");
        data.put("entityType", event.entity.getType().toString());
        data.put("entityId", event.entity.getId());
        data.put("hand", event.hand.name());

        recordEvent(EventType.ENTITY_INTERACTION, data);
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null) return;

        // Handle playback
        if (isPlayingBack && !isPaused && playbackSpeed.get() != PlaybackSpeed.FRAME_BY_FRAME) {
            handlePlaybackTick();
        }

        // Record position changes
        if (isRecording && recordPlayerActions.get()) {
            Vec3d currentPos = mc.player.getPos();
            // Only record if player moved significantly
            if (currentSession.isEmpty() ||
                currentPos.distanceTo(currentSession.get(currentSession.size() - 1).playerPosition) > 0.1) {

                Map<String, Object> data = new HashMap<>();
                data.put("action", "POSITION_CHANGE");
                data.put("x", currentPos.x);
                data.put("y", currentPos.y);
                data.put("z", currentPos.z);

                recordEvent(EventType.POSITION_CHANGE, data);
            }
        }

        // Take periodic inventory snapshots
        if (isRecording && recordInventoryChanges.get() && frameCounter % 20 == 0) { // Every second
            takeInventorySnapshot();
        }
    }

    private void handlePlaybackTick() {
        String sessionToPlay = currentSessionName;
        if (!savedSessions.containsKey(sessionToPlay)) {
            sessionToPlay = savedSessions.keySet().iterator().next();
        }

        List<RecordedEvent> sessionEvents = savedSessions.get(sessionToPlay);
        if (currentPlaybackIndex >= sessionEvents.size()) {
            if (loopPlayback.get()) {
                currentPlaybackIndex = 0;
                playbackStartTime = System.currentTimeMillis();
            } else {
                stopPlayback();
            }
            return;
        }

        RecordedEvent event = sessionEvents.get(currentPlaybackIndex);
        long expectedTime = (long) (event.relativeTime * playbackSpeed.get().multiplier);
        long actualTime = System.currentTimeMillis() - playbackStartTime;

        if (outOfSyncMode.get()) {
            expectedTime += syncOffset.get();
        }

        if (actualTime >= expectedTime) {
            processPlaybackEvent(event);
            currentPlaybackIndex++;
        }
    }

    @EventHandler
    private void onScreenOpen(OpenScreenEvent event) {
        if (!recordPlayerActions.get()) return;

        Map<String, Object> data = new HashMap<>();
        data.put("action", "SCREEN_OPEN");
        data.put("screenType", event.screen != null ? event.screen.getClass().getSimpleName() : "null");

        recordEvent(EventType.SCREEN_OPEN, data);
    }

    @EventHandler
    private void onGameLeft(GameLeftEvent event) {
        if (isRecording) {
            stopRecording();
        }
        if (isPlayingBack) {
            stopPlayback();
        }
    }

    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (!renderTimeline.get() || !isPlayingBack) return;

        // Render playback timeline and progress
        if (mc.player != null) {
            Vec3d playerPos = mc.player.getPos();
            Vector3d pos = new Vector3d();
            pos.set(playerPos.x, playerPos.y + 2.5, playerPos.z);

            if (NametagUtils.to2D(pos, 1.0)) {
                String sessionToPlay = currentSessionName;
                if (!savedSessions.containsKey(sessionToPlay)) {
                    sessionToPlay = savedSessions.keySet().iterator().next();
                }

                List<RecordedEvent> sessionEvents = savedSessions.get(sessionToPlay);
                double progress = (double) currentPlaybackIndex / sessionEvents.size() * 100;

                String progressText = String.format("▶️ %.1f%% [%d/%d]",
                    progress, currentPlaybackIndex, sessionEvents.size());

                Color color = playbackColor.get();
                TextRenderer.get().render(progressText, pos.x, pos.y, color, true);
            }
        }
    }

    @Override
    public String getInfoString() {
        if (!showHUD.get()) return null;

        RecordingMode currentMode = mode.get();
        StringBuilder info = new StringBuilder(currentMode.color + currentMode.title);

        if (isRecording) {
            long duration = (System.currentTimeMillis() - recordingStartTime) / 1000;
            info.append(String.format(" §7[§f%ds §7- §f%d §7events]", duration, currentSession.size()));
        } else if (isPlayingBack) {
            String sessionToPlay = currentSessionName;
            if (!savedSessions.containsKey(sessionToPlay)) {
                sessionToPlay = savedSessions.keySet().iterator().next();
            }

            List<RecordedEvent> sessionEvents = savedSessions.get(sessionToPlay);
            double progress = (double) currentPlaybackIndex / sessionEvents.size() * 100;
            info.append(String.format(" §7[§f%.1f%%§7]", progress));
        } else if (!detectedAnomalies.isEmpty()) {
            info.append(String.format(" §7[§e%d §7anomalies]", detectedAnomalies.size()));
        }

        return info.toString();
    }
}
