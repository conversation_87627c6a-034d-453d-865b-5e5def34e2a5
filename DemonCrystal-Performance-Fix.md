# DemonCrystal Performance Optimization Guide

## 🚨 CRITICAL FIXES NEEDED

### 1. Remove ALL Thread.sleep() Calls
**IMMEDIATE ACTION REQUIRED** - These are blocking the main client thread:

**Location 1:** Line 5974 in `placeTrapBlocks()`
```java
// REMOVE THIS:
try {
    Thread.sleep(50);
} catch (InterruptedException e) {
    Thread.currentThread().interrupt();
    break;
}

// REPLACE WITH:
// Use tick-based delays instead of blocking sleep
if (System.currentTimeMillis() - lastTrapPlaceTime < 50) continue;
lastTrapPlaceTime = System.currentTimeMillis();
```

**Location 2:** Line 6149 in `handlePlacement()`
```java
// REMOVE THIS:
if (multiCrystalDelay.get() > 0) {
    try {
        Thread.sleep(multiCrystalDelay.get() * 50);
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
    }
}

// REPLACE WITH:
if (multiCrystalDelay.get() > 0) {
    placeTicks = multiCrystalDelay.get();
    return; // Exit early, handle on next tick
}
```

**Location 3:** Line 6290 in `performBurstBreaking()`
```java
// REMOVE THIS:
if (burstDelay.get() > 0) {
    try {
        Thread.sleep(burstDelay.get() / burstTargets.size());
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        break;
    }
}

// REPLACE WITH:
// Use packet timing instead of blocking sleep
if (burstDelay.get() > 0) {
    updatePacketTiming();
    if (!canPerformPacketAction()) continue;
}
```

### 2. Optimize Entity Iteration - CRITICAL FOR FREEZING FIX

**Problem:** `mc.world.getEntities()` returns ALL entities in the world

**Solution:** Use ranged entity queries:
```java
// BEFORE (SLOW):
for (Entity entity : mc.world.getEntities()) {
    if (!(entity instanceof EndCrystalEntity crystal)) continue;
    if (mc.player.distanceTo(crystal) > breakRange.get()) continue;
    // Process entity
}

// AFTER (FAST):
Box searchBox = Box.of(mc.player.getPos(), breakRange.get() * 2, breakRange.get() * 2, breakRange.get() * 2);
for (Entity entity : mc.world.getOtherEntities(mc.player, searchBox, 
    e -> e instanceof EndCrystalEntity && !e.isRemoved())) {
    EndCrystalEntity crystal = (EndCrystalEntity) entity;
    // Process entity - already filtered
}
```

### 3. Add Entity Iteration Limits

**Add maximum limits to prevent overload:**
```java
// Add at top of methods that iterate entities
private static final int MAX_ENTITIES_PER_TICK = 50;
private static final int MAX_CRYSTALS_PER_TICK = 20;

// In findTargetCrystals():
int processedCount = 0;
for (Entity entity : mc.world.getOtherEntities(mc.player, searchBox)) {
    if (++processedCount > MAX_CRYSTALS_PER_TICK) break; // Safety limit
    // Process entity
}
```

### 4. Cache Entity Lists

**Instead of querying every tick:**
```java
// Add fields:
private List<EndCrystalEntity> cachedCrystals = new ArrayList<>();
private long lastCrystalCacheTime = 0;
private static final long CACHE_DURATION = 100; // 5 ticks

// In findTargetCrystals():
long currentTime = System.currentTimeMillis();
if (currentTime - lastCrystalCacheTime > CACHE_DURATION) {
    cachedCrystals.clear();
    // Rebuild cache with limited query
    lastCrystalCacheTime = currentTime;
}
// Use cached list instead of world query
```

### 5. Optimize Damage Calculations

**Reduce nested loop complexity:**
```java
// BEFORE (245 calculations per target):
for (int x = -3; x <= 3; x++) {
    for (int z = -3; z <= 3; z++) {
        for (int y = -2; y <= 2; y++) {
            BlockPos pos = targetBlockPos.add(x, y, z);
            // Calculate damage
        }
    }
}

// AFTER (Pre-calculated positions):
private static final List<Vec3i> DAMAGE_OFFSETS = Arrays.asList(
    new Vec3i(1, 0, 0), new Vec3i(-1, 0, 0), new Vec3i(0, 0, 1), new Vec3i(0, 0, -1),
    new Vec3i(1, 1, 0), new Vec3i(-1, 1, 0), new Vec3i(0, 1, 1), new Vec3i(0, 1, -1)
    // Only check the most relevant positions
);

for (Vec3i offset : DAMAGE_OFFSETS) {
    BlockPos pos = targetBlockPos.add(offset);
    // Much fewer calculations
}
```

### 6. Add Performance Monitoring

```java
// Add debugging to identify slow methods:
private void measurePerformance(String methodName, Runnable method) {
    if (!debugMode.get()) {
        method.run();
        return;
    }
    
    long start = System.nanoTime();
    method.run();
    long duration = System.nanoTime() - start;
    
    if (duration > 1_000_000) { // > 1ms
        info("§c[PERF] " + methodName + " took " + (duration / 1_000_000) + "ms");
    }
}

// Usage:
measurePerformance("findTarget", this::findTarget);
measurePerformance("findTargetCrystals", this::findTargetCrystals);
```

### 7. Optimize Collection Processing

**Limit processing per tick:**
```java
// In processInhibitPlacementQueue():
int processedCount = 0;
Iterator<Map.Entry<BlockPos, Integer>> iterator = inhibitPlacementQueue.entrySet().iterator();
while (iterator.hasNext() && processedCount < 5) { // Limit to 5 per tick
    processedCount++;
    // Process entry
}
```

### 8. Fix Enhanced Crystal Mechanics

**The verification loops are major performance killers:**
```java
// BEFORE (searches all entities repeatedly):
for (Entity entity : mc.world.getEntities()) {
    if (entity instanceof EndCrystalEntity && entity.getId() == crystal.getId()) {
        exists = true;
        break;
    }
}

// AFTER (use entity tracking):
private final Map<Integer, EndCrystalEntity> trackedCrystals = new HashMap<>();

// Update tracking when crystals are found
private void updateCrystalTracking() {
    if (System.currentTimeMillis() - lastTrackingUpdate > 200) { // Update every 10 ticks
        trackedCrystals.clear();
        Box searchBox = Box.of(mc.player.getPos(), 20, 10, 20);
        for (Entity entity : mc.world.getOtherEntities(mc.player, searchBox)) {
            if (entity instanceof EndCrystalEntity crystal) {
                trackedCrystals.put(crystal.getId(), crystal);
            }
        }
        lastTrackingUpdate = System.currentTimeMillis();
    }
}

// Use tracking instead of world search
private boolean verifyCrystalExists(EndCrystalEntity crystal) {
    return trackedCrystals.containsKey(crystal.getId()) && 
           !trackedCrystals.get(crystal.getId()).isRemoved();
}
```

## 🎯 Priority Implementation Order

1. **IMMEDIATE (Fix Freezing):**
   - Remove all Thread.sleep() calls
   - Add entity iteration limits
   - Optimize entity queries with bounding boxes

2. **HIGH PRIORITY:**
   - Cache entity lists
   - Optimize damage calculations
   - Limit collection processing per tick

3. **MEDIUM PRIORITY:**
   - Add performance monitoring
   - Optimize crystal tracking
   - Reduce unnecessary calculations

## 🔧 Quick Temporary Fix

Add this emergency brake to prevent complete freezing:
```java
// Add at start of onTick():
private long lastTickTime = 0;
private int heavyOperationCount = 0;

@EventHandler
private void onTick(TickEvent.Pre event) {
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastTickTime < 50) { // Minimum 50ms between ticks
        return; // Skip this tick if too fast
    }
    
    // Reset heavy operation counter every 100ms
    if (currentTime - lastTickTime > 100) {
        heavyOperationCount = 0;
    }
    
    if (heavyOperationCount > 10) { // Emergency brake
        return; // Skip heavy operations
    }
    
    lastTickTime = currentTime;
    // Rest of tick logic...
}
```

This guide addresses the root causes of your performance issues. The Thread.sleep() calls and unoptimized entity iterations are the primary culprits causing client freezing when opponents get close.
