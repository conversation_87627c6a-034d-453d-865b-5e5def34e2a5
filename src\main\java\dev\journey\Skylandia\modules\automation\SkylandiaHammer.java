package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.mixininterface.IPlayerInteractEntityC2SPacket;
import meteordevelopment.meteorclient.mixininterface.IPlayerMoveC2SPacket;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.friends.Friends;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.entity.EntityUtils;
import meteordevelopment.meteorclient.utils.render.NametagUtils;
import meteordevelopment.meteorclient.utils.render.RenderUtils;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.Items;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.c2s.play.*;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.RaycastContext;
import org.joml.Vector3d;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class SkylandiaHammer extends Module {
    // === SETTING GROUPS ===
    private final SettingGroup sgGeneral = settings.createGroup("General");
    private final SettingGroup sgTargeting = settings.createGroup("🎯 Targeting");
    private final SettingGroup sgAttack = settings.createGroup("⚔️ Attack");
    private final SettingGroup sgSpoofing = settings.createGroup("📡 Spoofing");
    private final SettingGroup sgRender = settings.createGroup("🎨 Render");
    private final SettingGroup sgDebug = settings.createGroup("🔧 Debug");

    // === GENERAL SETTINGS ===
    private final Setting<Boolean> autoSwitch = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-switch-mace")
        .description("Automatically switch to mace when attacking.")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> multiTarget = sgGeneral.add(new BoolSetting.Builder()
        .name("multi-target")
        .description("Attack multiple entities within range instead of just one.")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> onlyOnAttack = sgGeneral.add(new BoolSetting.Builder()
        .name("only-on-attack")
        .description("Only activate when manually attacking (original behavior).")
        .defaultValue(false)
        .build());

    // === TARGETING SETTINGS ===
    private final Setting<Double> targetRange = sgTargeting.add(new DoubleSetting.Builder()
        .name("🎯 target-range")
        .description("Maximum distance to scan for targets.")
        .defaultValue(6.0)
        .min(1.0)
        .sliderMax(15.0)
        .build());

    private final Setting<TargetPriority> targetPriority = sgTargeting.add(new EnumSetting.Builder<TargetPriority>()
        .name("🏹 target-priority")
        .description("How to prioritize targets.")
        .defaultValue(TargetPriority.Closest)
        .build());

    private final Setting<Set<EntityType<?>>> targetEntities = sgTargeting.add(new EntityTypeListSetting.Builder()
        .name("👹 target-entities")
        .description("Entity types to target with mace attacks.")
        .onlyAttackable()
        .defaultValue(EntityType.PLAYER, EntityType.ZOMBIE, EntityType.SKELETON, EntityType.CREEPER)
        .build());

    private final Setting<Boolean> ignoreFriends = sgTargeting.add(new BoolSetting.Builder()
        .name("ignore-friends")
        .description("Don't attack friends.")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> ignoreNaked = sgTargeting.add(new BoolSetting.Builder()
        .name("ignore-naked")
        .description("Don't attack players without armor.")
        .defaultValue(false)
        .build());

    private final Setting<Boolean> lineOfSight = sgTargeting.add(new BoolSetting.Builder()
        .name("line-of-sight")
        .description("Only target entities you can see.")
        .defaultValue(true)
        .build());

    // === ATTACK SETTINGS ===
    private final Setting<Integer> attackDelay = sgAttack.add(new IntSetting.Builder()
        .name("attack-delay")
        .description("Delay between attacks in ticks.")
        .defaultValue(5)
        .min(0)
        .sliderMax(20)
        .build());

    private final Setting<Integer> maxTargetsPerTick = sgAttack.add(new IntSetting.Builder()
        .name("max-targets-per-tick")
        .description("Maximum number of targets to attack per tick.")
        .defaultValue(3)
        .min(1)
        .sliderMax(10)
        .build());

    private final Setting<Boolean> smartAttack = sgAttack.add(new BoolSetting.Builder()
        .name("smart-attack")
        .description("Use intelligent attack timing and targeting.")
        .defaultValue(true)
        .build());

    // === SPOOFING SETTINGS ===
    private final Setting<Boolean> useBlinkQueue = sgSpoofing.add(new BoolSetting.Builder()
        .name("blink-packet-queue")
        .description("Use packet queue for position spoofing.")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> preventDeath = sgSpoofing.add(new BoolSetting.Builder()
        .name("prevent-fall-damage")
        .description("Prevent fall damage from spoofing.")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> maxPower = sgSpoofing.add(new BoolSetting.Builder()
        .name("max-mace-power")
        .description("Use maximum mace power (Paper servers).")
        .defaultValue(false)
        .build());

    private final Setting<Integer> fallHeight = sgSpoofing.add(new IntSetting.Builder()
        .name("fall-height")
        .description("Simulated fall height for mace damage.")
        .defaultValue(22)
        .min(1).max(170)
        .visible(() -> !maxPower.get())
        .build());

    // === RENDER SETTINGS ===
    private final Setting<Boolean> renderTargets = sgRender.add(new BoolSetting.Builder()
        .name("render-targets")
        .description("Highlight targeted entities.")
        .defaultValue(true)
        .build());

    private final Setting<SettingColor> targetColor = sgRender.add(new ColorSetting.Builder()
        .name("target-color")
        .description("Color for target highlighting.")
        .defaultValue(new SettingColor(255, 50, 50, 100))
        .visible(renderTargets::get)
        .build());

    private final Setting<Boolean> renderRange = sgRender.add(new BoolSetting.Builder()
        .name("render-range")
        .description("Show attack range sphere.")
        .defaultValue(true)
        .build());

    private final Setting<SettingColor> rangeColor = sgRender.add(new ColorSetting.Builder()
        .name("range-color")
        .description("Color for range visualization.")
        .defaultValue(new SettingColor(100, 100, 255, 50))
        .visible(renderRange::get)
        .build());

    private final Setting<Boolean> renderTracers = sgRender.add(new BoolSetting.Builder()
        .name("render-tracers")
        .description("Draw lines to targets.")
        .defaultValue(false)
        .build());

    private final Setting<SettingColor> tracerColor = sgRender.add(new ColorSetting.Builder()
        .name("tracer-color")
        .description("Color for tracer lines.")
        .defaultValue(new SettingColor(255, 255, 0, 150))
        .visible(renderTracers::get)
        .build());

    // === DEBUG SETTINGS ===
    private final Setting<Boolean> debugMode = sgDebug.add(new BoolSetting.Builder()
        .name("debug-mode")
        .description("Show debug information.")
        .defaultValue(false)
        .build());

    private final Setting<Boolean> debugText = sgDebug.add(new BoolSetting.Builder()
        .name("debug-text")
        .description("Show debug text overlay.")
        .defaultValue(false)
        .visible(debugMode::get)
        .build());

    // === ENUMS ===
    public enum TargetPriority {
        Closest("Closest"),
        LowestHealth("Lowest Health"),
        HighestHealth("Highest Health"),
        Players("Players First"),
        Mobs("Mobs First");

        private final String title;

        TargetPriority(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return title;
        }
    }

    // === INTERNAL VARIABLES ===
    private final List<Packet<?>> blinkQueue = new ArrayList<>();
    private final List<Entity> validTargets = new ArrayList<>();
    private final Map<Entity, Long> lastAttackTime = new ConcurrentHashMap<>();
    private final Vector3d pos = new Vector3d();

    private Vec3d previouspos;
    private Entity currentTarget;
    private int attackTimer = 0;

    public SkylandiaHammer() {
        super(Skylandia.Automation, "SkylandiaHammer", "Enhanced mace combat with multi-targeting, smart detection, and visual feedback.");
    }

    // === ENTITY DETECTION & SELECTION SYSTEM ===

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // Update attack timer
        if (attackTimer > 0) attackTimer--;

        // Find valid targets
        findValidTargets();

        // Auto-attack if not in only-on-attack mode
        if (!onlyOnAttack.get() && !validTargets.isEmpty() && attackTimer <= 0) {
            performMultiTargetAttack();
        }
    }

    private void findValidTargets() {
        validTargets.clear();

        if (mc.world == null || mc.player == null) return;

        for (Entity entity : mc.world.getEntities()) {
            if (!isValidTarget(entity)) continue;

            double distance = mc.player.distanceTo(entity);
            if (distance > targetRange.get()) continue;

            // Line of sight check
            if (lineOfSight.get() && !hasLineOfSight(entity)) continue;

            validTargets.add(entity);
        }

        // Sort targets by priority
        sortTargetsByPriority();
    }

    private boolean isValidTarget(Entity entity) {
        if (!(entity instanceof LivingEntity living)) return false;
        if (entity == mc.player) return false;
        if (!targetEntities.get().contains(entity.getType())) return false;
        if (entity.isRemoved() || !entity.isAlive()) return false;

        // Friend check
        if (ignoreFriends.get() && entity instanceof PlayerEntity player && Friends.get().isFriend(player)) {
            return false;
        }

        // Naked player check
        if (ignoreNaked.get() && entity instanceof PlayerEntity player) {
            boolean hasArmor = false;
            for (var stack : player.getArmorItems()) {
                if (!stack.isEmpty()) {
                    hasArmor = true;
                    break;
                }
            }
            if (!hasArmor) return false;
        }

        return true;
    }

    private boolean hasLineOfSight(Entity entity) {
        Vec3d start = mc.player.getEyePos();
        Vec3d end = entity.getEyePos();

        HitResult result = mc.world.raycast(new RaycastContext(
            start, end,
            RaycastContext.ShapeType.COLLIDER,
            RaycastContext.FluidHandling.NONE,
            mc.player
        ));

        return result.getType() != HitResult.Type.BLOCK;
    }

    private void sortTargetsByPriority() {
        validTargets.sort((a, b) -> {
            double scoreA = calculateTargetScore(a);
            double scoreB = calculateTargetScore(b);
            return Double.compare(scoreA, scoreB);
        });
    }

    private double calculateTargetScore(Entity entity) {
        double distance = mc.player.distanceTo(entity);

        return switch (targetPriority.get()) {
            case Closest -> distance;
            case LowestHealth -> entity instanceof LivingEntity living ? living.getHealth() : distance;
            case HighestHealth -> entity instanceof LivingEntity living ? 100 - living.getHealth() : distance;
            case Players -> entity instanceof PlayerEntity ? distance * 0.5 : distance * 2;
            case Mobs -> entity instanceof PlayerEntity ? distance * 2 : distance * 0.5;
        };
    }

    // === MULTI-TARGET ATTACK SYSTEM ===

    private void performMultiTargetAttack() {
        if (!mc.player.getMainHandStack().getItem().equals(Items.MACE)) {
            if (autoSwitch.get()) {
                autoSwitchToMace();
            } else return;
        }

        int attackedThisTick = 0;
        int maxAttacks = maxTargetsPerTick.get();

        for (Entity target : validTargets) {
            if (attackedThisTick >= maxAttacks) break;

            // Check if we can attack this target
            if (!canAttackTarget(target)) continue;

            // Perform mace attack on target
            if (performMaceAttack(target)) {
                attackedThisTick++;
                lastAttackTime.put(target, System.currentTimeMillis());

                if (debugMode.get()) {
                    System.out.println("[SkylandiaHammer] Attacked: " + getEntityName(target));
                }
            }
        }

        if (attackedThisTick > 0) {
            attackTimer = attackDelay.get();
        }
    }

    private boolean canAttackTarget(Entity target) {
        if (!(target instanceof LivingEntity living)) return false;

        // Check attack delay
        Long lastAttack = lastAttackTime.get(target);
        if (lastAttack != null && System.currentTimeMillis() - lastAttack < attackDelay.get() * 50) {
            return false;
        }

        // Safety checks
        if (preventDeath.get() && living.isBlocking() && living.blockedByShield(living.getRecentDamageSource())) {
            return false;
        }

        if (living.isInvulnerable() || (living instanceof PlayerEntity player && player.isInCreativeMode())) {
            return false;
        }

        return true;
    }

    private boolean performMaceAttack(Entity target) {
        if (!(target instanceof LivingEntity)) return false;

        previouspos = mc.player.getPos();
        int blocks = getMaxHeightAbovePlayer();
        double targetHeight = mc.player.getY() + blocks;

        BlockPos air1 = mc.player.getBlockPos().add(0, blocks, 0);
        BlockPos air2 = mc.player.getBlockPos().add(0, blocks + 1, 0);
        if (!isOpenAir(air1) || !isOpenAir(air2)) return false;

        // Perform the spoofing attack
        if (useBlinkQueue.get()) {
            queueSpoofPackets(targetHeight);
            sendBlinkQueue();
        } else {
            teleportAndBack(targetHeight);
        }

        // Send attack packet
        mc.player.networkHandler.sendPacket(PlayerInteractEntityC2SPacket.attack(target, mc.player.isSneaking()));
        mc.player.swingHand(Hand.MAIN_HAND);

        return true;
    }

    @EventHandler
    private void onSendPacket(PacketEvent.Send event) {
        if (!onlyOnAttack.get()) return; // Only intercept in manual mode

        if (mc.player == null) return;
        if (!(event.packet instanceof IPlayerInteractEntityC2SPacket packet)) return;
        if (packet.meteor$getType() != PlayerInteractEntityC2SPacket.InteractType.ATTACK) return;
        if (!(packet.meteor$getEntity() instanceof LivingEntity targetEntity)) return;

        // Store the manually targeted entity
        currentTarget = targetEntity;

        if (!mc.player.getMainHandStack().getItem().equals(Items.MACE)) {
            if (autoSwitch.get()) {
                autoSwitchToMace();
            } else return;
        }

        // Safety checks (blocking, invulnerable, creative)
        if (preventDeath.get() && ((targetEntity.isBlocking() && targetEntity.blockedByShield(targetEntity.getRecentDamageSource()))
            || targetEntity.isInvulnerable() || targetEntity.isInCreativeMode())) return;

        previouspos = mc.player.getPos();
        int blocks = getMaxHeightAbovePlayer();
        double targetHeight = mc.player.getY() + blocks;

        BlockPos air1 = mc.player.getBlockPos().add(0, blocks, 0);
        BlockPos air2 = mc.player.getBlockPos().add(0, blocks + 1, 0);
        if (!isOpenAir(air1) || !isOpenAir(air2)) return;

        if (useBlinkQueue.get()) {
            queueSpoofPackets(targetHeight);
            sendBlinkQueue();
        } else {
            teleportAndBack(targetHeight);
        }

        // If multi-target is enabled, attack nearby targets too
        if (multiTarget.get()) {
            findValidTargets();
            performMultiTargetAttack();
        }
    }

    private String getEntityName(Entity entity) {
        if (entity instanceof PlayerEntity player) {
            return player.getGameProfile().getName();
        }
        return entity.getType().toString();
    }

    // === VISUAL RENDERING FEATURES ===

    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;

        // Render attack range
        if (renderRange.get()) {
            renderAttackRange(event);
        }

        // Render targets
        if (renderTargets.get()) {
            renderTargetHighlights(event);
        }

        // Render tracers
        if (renderTracers.get()) {
            renderTracerLines(event);
        }

        // Render debug information
        if (debugMode.get() && debugText.get()) {
            renderDebugInfo(event);
        }
    }

    private void renderAttackRange(Render3DEvent event) {
        double range = targetRange.get();
        Vec3d center = mc.player.getPos();

        // Render range sphere (simplified as circles at different heights)
        for (int y = -2; y <= 2; y++) {
            double yOffset = y * 0.5;
            double adjustedRange = Math.sqrt(range * range - yOffset * yOffset);
            if (adjustedRange <= 0) continue;

            int segments = 32;
            for (int i = 0; i < segments; i++) {
                double angle1 = (i * 2 * Math.PI) / segments;
                double angle2 = ((i + 1) * 2 * Math.PI) / segments;

                double x1 = center.x + Math.cos(angle1) * adjustedRange;
                double z1 = center.z + Math.sin(angle1) * adjustedRange;
                double x2 = center.x + Math.cos(angle2) * adjustedRange;
                double z2 = center.z + Math.sin(angle2) * adjustedRange;

                event.renderer.line(
                    x1, center.y + yOffset, z1,
                    x2, center.y + yOffset, z2,
                    rangeColor.get()
                );
            }
        }
    }

    private void renderTargetHighlights(Render3DEvent event) {
        for (Entity target : validTargets) {
            // Highlight target with box
            event.renderer.box(
                target.getBoundingBox().expand(0.1),
                targetColor.get(),
                targetColor.get(),
                meteordevelopment.meteorclient.renderer.ShapeMode.Both,
                0
            );

            // Render health bar above target
            if (target instanceof LivingEntity living) {
                renderHealthBar(event, living);
            }
        }

        // Highlight current manual target differently
        if (currentTarget != null && onlyOnAttack.get()) {
            Color currentTargetColor = new Color(255, 255, 0, 150);
            event.renderer.box(
                currentTarget.getBoundingBox().expand(0.15),
                currentTargetColor,
                currentTargetColor,
                meteordevelopment.meteorclient.renderer.ShapeMode.Both,
                0
            );
        }
    }

    private void renderHealthBar(Render3DEvent event, LivingEntity entity) {
        Vec3d entityPos = entity.getPos().add(0, entity.getHeight() + 0.5, 0);
        pos.set(entityPos.x, entityPos.y, entityPos.z);

        if (!NametagUtils.to2D(pos, 1.0)) return;

        float health = entity.getHealth();
        float maxHealth = entity.getMaxHealth();
        float healthPercent = MathHelper.clamp(health / maxHealth, 0, 1);

        double barWidth = 30;
        double barHeight = 4;

        // Background
        event.renderer.quad(
            pos.x - barWidth / 2, pos.y - barHeight / 2, 0,
            pos.x + barWidth / 2, pos.y - barHeight / 2, 0,
            pos.x + barWidth / 2, pos.y + barHeight / 2, 0,
            pos.x - barWidth / 2, pos.y + barHeight / 2, 0,
            new Color(0, 0, 0, 100)
        );

        // Health bar
        Color healthColor = healthPercent > 0.6 ? new Color(0, 255, 0, 200) :
                           healthPercent > 0.3 ? new Color(255, 255, 0, 200) :
                           new Color(255, 0, 0, 200);

        double healthWidth = barWidth * healthPercent;
        event.renderer.quad(
            pos.x - barWidth / 2, pos.y - barHeight / 2, 0,
            pos.x - barWidth / 2 + healthWidth, pos.y - barHeight / 2, 0,
            pos.x - barWidth / 2 + healthWidth, pos.y + barHeight / 2, 0,
            pos.x - barWidth / 2, pos.y + barHeight / 2, 0,
            healthColor
        );

        // Health text
        TextRenderer textRenderer = TextRenderer.get();
        String healthText = String.format("%.1f/%.1f", health, maxHealth);
        double textWidth = textRenderer.getWidth(healthText, false);

        textRenderer.render(
            healthText,
            pos.x - textWidth / 2.0,
            pos.y + barHeight / 2 + 2,
            new Color(255, 255, 255, 255),
            false
        );
    }

    private void renderTracerLines(Render3DEvent event) {
        Vec3d playerPos = mc.player.getEyePos();

        for (Entity target : validTargets) {
            Vec3d targetPos = target.getEyePos();
            event.renderer.line(
                playerPos.x, playerPos.y, playerPos.z,
                targetPos.x, targetPos.y, targetPos.z,
                tracerColor.get()
            );
        }
    }

    private void renderDebugInfo(Render3DEvent event) {
        if (mc.player == null) return;

        Vec3d debugPos = mc.player.getPos().add(0, mc.player.getHeight() + 2, 0);
        pos.set(debugPos.x, debugPos.y, debugPos.z);

        if (!NametagUtils.to2D(pos, 1.0)) return;

        TextRenderer textRenderer = TextRenderer.get();
        List<String> debugLines = new ArrayList<>();

        debugLines.add("§6[SkylandiaHammer Debug]");
        debugLines.add("§7Targets: §f" + validTargets.size());
        debugLines.add("§7Range: §f" + String.format("%.1f", targetRange.get()));
        debugLines.add("§7Attack Timer: §f" + attackTimer);
        debugLines.add("§7Mode: §f" + (onlyOnAttack.get() ? "Manual" : "Auto"));

        if (currentTarget != null) {
            debugLines.add("§7Current: §f" + getEntityName(currentTarget));
        }

        double yOffset = 0;
        for (String line : debugLines) {
            double textWidth = textRenderer.getWidth(line, false);
            textRenderer.render(
                line,
                pos.x - textWidth / 2.0,
                pos.y + yOffset,
                new Color(255, 255, 255, 255),
                false
            );
            yOffset += 10;
        }
    }

    private void autoSwitchToMace() {
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).getItem() == Items.MACE) {
                mc.player.getInventory().selectedSlot = i;
                break;
            }
        }
    }

    private void queueSpoofPackets(double height) {
        PlayerMoveC2SPacket up = new PlayerMoveC2SPacket.PositionAndOnGround(
            mc.player.getX(), height, mc.player.getZ(), false, false);
        PlayerMoveC2SPacket down = new PlayerMoveC2SPacket.PositionAndOnGround(
            previouspos.getX(), previouspos.getY() + (preventDeath.get() ? 0.25 : 0), previouspos.getZ(), false, false);

        ((IPlayerMoveC2SPacket) up).meteor$setTag(1337);
        ((IPlayerMoveC2SPacket) down).meteor$setTag(1337);

        blinkQueue.add(up);
        blinkQueue.add(down);
    }

    private void sendBlinkQueue() {
        for (Packet<?> packet : blinkQueue) {
            mc.player.networkHandler.sendPacket(packet);
        }
        blinkQueue.clear();
        if (preventDeath.get()) {
            mc.player.setVelocity(mc.player.getVelocity().x, 0.1, mc.player.getVelocity().z);
            mc.player.fallDistance = 0;
        }
    }

    private void teleportAndBack(double height) {
        PlayerMoveC2SPacket up = new PlayerMoveC2SPacket.PositionAndOnGround(
            mc.player.getX(), height, mc.player.getZ(), false, false);
        PlayerMoveC2SPacket down = new PlayerMoveC2SPacket.PositionAndOnGround(
            previouspos.getX(), previouspos.getY() + (preventDeath.get() ? 0.25 : 0), previouspos.getZ(), false, false);

        ((IPlayerMoveC2SPacket) up).meteor$setTag(1337);
        ((IPlayerMoveC2SPacket) down).meteor$setTag(1337);

        mc.player.networkHandler.sendPacket(up);
        mc.player.networkHandler.sendPacket(down);

        if (preventDeath.get()) {
            mc.player.setVelocity(mc.player.getVelocity().x, 0.1, mc.player.getVelocity().z);
            mc.player.fallDistance = 0;
        }
    }

    private int getMaxHeightAbovePlayer() {
        BlockPos playerPos = mc.player.getBlockPos();
        int height = maxPower.get() ? 170 : fallHeight.get();
        for (int i = height; i > 0; i--) {
            BlockPos checkPos = playerPos.up(i);
            if (isOpenAir(checkPos)) return i;
        }
        return 0;
    }

    private boolean isOpenAir(BlockPos pos) {
        return mc.world.getBlockState(pos).isReplaceable()
            && mc.world.getFluidState(pos).isEmpty()
            && !mc.world.getBlockState(pos).isOf(Blocks.POWDER_SNOW);
    }

    // === MODULE LIFECYCLE ===

    @Override
    public void onActivate() {
        validTargets.clear();
        lastAttackTime.clear();
        currentTarget = null;
        attackTimer = 0;
    }

    @Override
    public void onDeactivate() {
        validTargets.clear();
        lastAttackTime.clear();
        currentTarget = null;
        blinkQueue.clear();
    }

    // === UTILITY METHODS ===

    public int getTargetCount() {
        return validTargets.size();
    }

    public Entity getCurrentTarget() {
        return currentTarget;
    }

    public boolean isTargeting(Entity entity) {
        return validTargets.contains(entity);
    }

    public double getAttackRange() {
        return targetRange.get();
    }
}
