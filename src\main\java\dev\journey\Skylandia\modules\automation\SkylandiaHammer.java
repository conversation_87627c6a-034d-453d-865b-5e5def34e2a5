package dev.journey.Skylandia.modules.automation;

// File/class name fix: Rename file to SkylandiaHammer.java to match class name
import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.mixininterface.IPlayerInteractEntityC2SPacket;
import meteordevelopment.meteorclient.mixininterface.IPlayerMoveC2SPacket;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.entity.LivingEntity;
import net.minecraft.item.Items;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.c2s.play.*;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;

import java.util.ArrayList;
import java.util.List;

public class SkylandiaHammer extends Module {
    private final SettingGroup general = settings.createGroup("General");
    private final SettingGroup spoofing = settings.createGroup("Spoofing");

    private final Setting<Boolean> autoSwitch = general.add(new BoolSetting.Builder()
        .name("Auto Switch to Mace")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> useBlinkQueue = spoofing.add(new BoolSetting.Builder()
        .name("Blink Packet Queue")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> preventDeath = spoofing.add(new BoolSetting.Builder()
        .name("Prevent Fall Damage")
        .defaultValue(true)
        .build());

    private final Setting<Boolean> maxPower = spoofing.add(new BoolSetting.Builder()
        .name("Max Mace Power (Paper)")
        .defaultValue(false)
        .build());

    private final Setting<Integer> fallHeight = spoofing.add(new IntSetting.Builder()
        .name("Fall Height")
        .defaultValue(22)
        .min(1).max(170)
        .visible(() -> !maxPower.get())
        .build());

    private final List<Packet<?>> blinkQueue = new ArrayList<>();
    private Vec3d previouspos;

    public SkylandiaHammer() {
        super(Skylandia.Automation, "SkylandiaHammer", "Spoofs mace fall damage with packet manipulation and queue control.");
    }

    @EventHandler
    private void onSendPacket(PacketEvent.Send event) {
        if (mc.player == null) return;
        if (!(event.packet instanceof IPlayerInteractEntityC2SPacket packet)) return;
        if (packet.meteor$getType() != PlayerInteractEntityC2SPacket.InteractType.ATTACK) return;
        if (!(packet.meteor$getEntity() instanceof LivingEntity targetEntity)) return;
        if (!mc.player.getMainHandStack().getItem().equals(Items.MACE)) {
            if (autoSwitch.get()) {
                autoSwitchToMace();
            } else return;
        }

        // Safety checks (blocking, invulnerable, creative)
        if (preventDeath.get() && ((targetEntity.isBlocking() && targetEntity.blockedByShield(targetEntity.getRecentDamageSource()))
            || targetEntity.isInvulnerable() || targetEntity.isInCreativeMode())) return;

        previouspos = mc.player.getPos();
        int blocks = getMaxHeightAbovePlayer();
        double targetHeight = mc.player.getY() + blocks;

        BlockPos air1 = mc.player.getBlockPos().add(0, blocks, 0);
        BlockPos air2 = mc.player.getBlockPos().add(0, blocks + 1, 0);
        if (!isOpenAir(air1) || !isOpenAir(air2)) return;

        if (useBlinkQueue.get()) {
            queueSpoofPackets(targetHeight);
            sendBlinkQueue();
        } else {
            teleportAndBack(targetHeight);
        }
    }

    private void autoSwitchToMace() {
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).getItem() == Items.MACE) {
                mc.player.getInventory().selectedSlot = i;
                break;
            }
        }
    }

    private void queueSpoofPackets(double height) {
        PlayerMoveC2SPacket up = new PlayerMoveC2SPacket.PositionAndOnGround(
            mc.player.getX(), height, mc.player.getZ(), false, false);
        PlayerMoveC2SPacket down = new PlayerMoveC2SPacket.PositionAndOnGround(
            previouspos.getX(), previouspos.getY() + (preventDeath.get() ? 0.25 : 0), previouspos.getZ(), false, false);

        ((IPlayerMoveC2SPacket) up).meteor$setTag(1337);
        ((IPlayerMoveC2SPacket) down).meteor$setTag(1337);

        blinkQueue.add(up);
        blinkQueue.add(down);
    }

    private void sendBlinkQueue() {
        for (Packet<?> packet : blinkQueue) {
            mc.player.networkHandler.sendPacket(packet);
        }
        blinkQueue.clear();
        if (preventDeath.get()) {
            mc.player.setVelocity(mc.player.getVelocity().x, 0.1, mc.player.getVelocity().z);
            mc.player.fallDistance = 0;
        }
    }

    private void teleportAndBack(double height) {
        PlayerMoveC2SPacket up = new PlayerMoveC2SPacket.PositionAndOnGround(
            mc.player.getX(), height, mc.player.getZ(), false, false);
        PlayerMoveC2SPacket down = new PlayerMoveC2SPacket.PositionAndOnGround(
            previouspos.getX(), previouspos.getY() + (preventDeath.get() ? 0.25 : 0), previouspos.getZ(), false, false);

        ((IPlayerMoveC2SPacket) up).meteor$setTag(1337);
        ((IPlayerMoveC2SPacket) down).meteor$setTag(1337);

        mc.player.networkHandler.sendPacket(up);
        mc.player.networkHandler.sendPacket(down);

        if (preventDeath.get()) {
            mc.player.setVelocity(mc.player.getVelocity().x, 0.1, mc.player.getVelocity().z);
            mc.player.fallDistance = 0;
        }
    }

    private int getMaxHeightAbovePlayer() {
        BlockPos playerPos = mc.player.getBlockPos();
        int height = maxPower.get() ? 170 : fallHeight.get();
        for (int i = height; i > 0; i--) {
            BlockPos checkPos = playerPos.up(i);
            if (isOpenAir(checkPos)) return i;
        }
        return 0;
    }

    private boolean isOpenAir(BlockPos pos) {
        return mc.world.getBlockState(pos).isReplaceable()
            && mc.world.getFluidState(pos).isEmpty()
            && !mc.world.getBlockState(pos).isOf(Blocks.POWDER_SNOW);
    }
}
