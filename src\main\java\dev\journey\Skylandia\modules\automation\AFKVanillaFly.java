package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import net.minecraft.item.ItemStack;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.player.PlayerEntity;
import java.util.Collections;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import java.util.List;
import java.util.ArrayList;
import dev.journey.Skylandia.modules.exploration.Trails;
import net.minecraft.util.math.ChunkPos;
import java.util.Set;
import java.util.HashSet;
import java.util.Queue;
import java.util.LinkedList;

public class AFKVanillaFly extends Module {
    // Pathfinding state
    private boolean pathGenerated = false;

    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final Setting<Boolean> pathThroughOldChunks = sgGeneral.add(new BoolSetting.Builder()
        .name("Path Through Old Chunks")
        .description("When enabled, will generate a path through old chunks using Trails module data.")
        .defaultValue(false)
        .build()
    );
    private final Setting<AutoFireworkMode> fireworkMode = sgGeneral.add(new EnumSetting.Builder<AutoFireworkMode>()
        .name("Auto Firework Mode")
        .description("Choose between velocity-based or timed firework usage.")
        .defaultValue(AutoFireworkMode.VELOCITY)
        .build()
    );
    private final Setting<Integer> fireworkDelay = sgGeneral.add(new IntSetting.Builder()
        .name("Timed Delay (ms)")
        .description("How long to wait between fireworks when using Timed Delay.")
        .defaultValue(3000)
        .sliderRange(0, 6000)
        .visible(() -> fireworkMode.get() == AutoFireworkMode.TIMED_DELAY)
        .build()
    );
    private final Setting<Double> velocityThreshold = sgGeneral.add(new DoubleSetting.Builder()
        .name("Velocity Threshold")
        .description("Use a firework if your horizontal speed is below this value.")
        .defaultValue(0.7)
        .sliderRange(0.1, 2.0)
        .visible(() -> fireworkMode.get() == AutoFireworkMode.VELOCITY)
        .build()
    );
    // New: Enable DemonCrystal-style movement
    private final Setting<Boolean> enableTargetFly = sgGeneral.add(new BoolSetting.Builder()
        .name("Enable Target Fly")
        .description("Fly toward a selected block using velocity-based movement.")
        .defaultValue(false)
        .build()
    );
    private final Setting<Double> flySpeed = sgGeneral.add(new DoubleSetting.Builder()
        .name("Target Fly Speed")
        .description("Speed to use when flying toward the target block.")
        .defaultValue(0.7)
        .min(0.1)
        .sliderMax(2.0)
        .visible(enableTargetFly::get)
        .build()
    );
    private BlockPos flyTarget = null;
    // Path following
    private final Setting<Boolean> enablePathFollow = sgGeneral.add(new BoolSetting.Builder()
        .name("Enable Path Follow")
        .description("Follow a list of waypoints instead of a single target.")
        .defaultValue(false)
        .build()
    );
    private final Setting<Boolean> enableObstacleAvoid = sgGeneral.add(new BoolSetting.Builder()
        .name("Obstacle Avoidance")
        .description("Pause or reroute if a block is detected in the flight path.")
        .defaultValue(false)
        .build()
    );
    private final Setting<Boolean> enableAutoLand = sgGeneral.add(new BoolSetting.Builder()
        .name("Auto-Land at End")
        .description("Gently descend and land at the final waypoint.")
        .defaultValue(false)
        .build()
    );
    private final Setting<Boolean> enableResourceMonitor = sgGeneral.add(new BoolSetting.Builder()
        .name("Resource Monitor")
        .description("Warn or auto-disable if fireworks or elytra durability are low.")
        .defaultValue(true)
        .build()
    );
    private final Setting<Boolean> enableRandomize = sgGeneral.add(new BoolSetting.Builder()
        .name("Randomize Movement")
        .description("Add small randomization to path/speed for anti-AFK detection.")
        .defaultValue(false)
        .build()
    );
    private final Setting<Boolean> enableAutoResume = sgGeneral.add(new BoolSetting.Builder()
        .name("Auto-Resume Flight")
        .description("If interrupted, attempt to resume flight and path.")
        .defaultValue(true)
        .build()
    );
    // Waypoint path (set externally)
    private List<BlockPos> waypoints = new ArrayList<>();
    private int currentWaypoint = 0;
    private long lastRocketUse = 0;
    private boolean launched = false;
    private double yTarget = -1;
    private float targetPitch = 0;

    public AFKVanillaFly() {
        super(Skylandia.Automation, "AFKVanillaFly", "Maintains a level Y-flight with fireworks and smooth pitch control.");
    }

    @Override
    public void onActivate() {
        launched = false;
        yTarget = -1;

        if (mc.player == null || !mc.player.isGliding()) {
            info("You must be using elytra before enabling AFKVanillaFly.");
        }
    }

    // this method is now then default logic, it did not need to be called in TrailFollower
    public void tickFlyLogic() {
        if (mc.player == null) return;

        double currentY = mc.player.getY();

        // Path Through Old Chunks: generate path if needed
        if (pathThroughOldChunks.get() && !pathGenerated) {
            BlockPos start = mc.player.getBlockPos();
            BlockPos goal = null;
            if (enablePathFollow.get() && !waypoints.isEmpty()) {
                goal = waypoints.get(waypoints.size() - 1);
            } else if (flyTarget != null) {
                goal = flyTarget;
            }
            if (goal != null) {
                List<BlockPos> path = findPathThroughOldChunks(start, goal);
                if (path != null && !path.isEmpty()) {
                    setWaypoints(path);
                    pathGenerated = true;
                    info("Path through old chunks generated: " + path.size() + " waypoints.");
                } else {
                    info("No valid path through old chunks found.");
                }
            }
        }

        // Resource monitoring (elytra only)
        if (enableResourceMonitor.get()) {
            if (!hasGoodElytra()) {
                info("Low elytra durability! Disabling.");
                this.toggle();
                return;
            }
        }

        // Auto-resume: if not gliding but should be, try to resume
        if (enableAutoResume.get() && enableTargetFly.get() && (enablePathFollow.get() ? currentWaypoint < waypoints.size() : flyTarget != null)) {
            if (!mc.player.isGliding()) {
                // Try to jump and use firework to resume flight
                mc.player.jump();
                if (hasEnoughFireworks()) tryUseFirework();
                return;
            }
        }

        // Path following logic
        BlockPos target = flyTarget;
        if (enablePathFollow.get() && !waypoints.isEmpty()) {
            if (currentWaypoint >= waypoints.size()) {
                if (enableAutoLand.get()) {
                    gentlyDescendAndLand();
                }
                return;
            }
            target = waypoints.get(currentWaypoint);
        }

        boolean hasFireworks = hasEnoughFireworks();

        if (enableTargetFly.get() && target != null && mc.player.isGliding()) {
            // Move toward current waypoint using velocity-based propulsion
            Vec3d playerPos = mc.player.getPos();
            Vec3d targetCenter = new Vec3d(target.getX() + 0.5, currentY, target.getZ() + 0.5);
            double dx = targetCenter.x - playerPos.x;
            double dz = targetCenter.z - playerPos.z;
            double dist = Math.sqrt(dx * dx + dz * dz);
            // Obstacle avoidance with sidestep
            if (enableObstacleAvoid.get() && isObstacleInPath(playerPos, targetCenter)) {
                info("Obstacle detected! Attempting sidestep.");
                Vec3d left = playerPos.add(-dz / dist, 0, dx / dist);
                Vec3d right = playerPos.add(dz / dist, 0, -dx / dist);
                if (!isObstacleInPath(left, targetCenter)) {
                    mc.player.setVelocity((left.x - playerPos.x) * 0.5, mc.player.getVelocity().y, (left.z - playerPos.z) * 0.5);
                } else if (!isObstacleInPath(right, targetCenter)) {
                    mc.player.setVelocity((right.x - playerPos.x) * 0.5, mc.player.getVelocity().y, (right.z - playerPos.z) * 0.5);
                } else {
                    mc.player.setVelocity(0, mc.player.getVelocity().y, 0);
                }
                return;
            }
            double speed = flySpeed.get();
            double jitterX = 0, jitterZ = 0;
            if (enableRandomize.get()) {
                speed *= (0.95 + Math.random() * 0.1);
                double angle = Math.random() * 2 * Math.PI;
                double mag = 0.1 * Math.random();
                jitterX = Math.cos(angle) * mag;
                jitterZ = Math.sin(angle) * mag;
            }
            if (dist < 1.0) {
                mc.player.setVelocity(0, mc.player.getVelocity().y, 0);
                if (enablePathFollow.get() && !waypoints.isEmpty()) currentWaypoint++;
            } else {
                double vx = (dx / dist) * speed + jitterX;
                double vz = (dz / dist) * speed + jitterZ;
                mc.player.setVelocity(vx, mc.player.getVelocity().y, vz);
            }
        } else if (mc.player.isGliding() && !enableTargetFly.get()) {
            double yawRad = Math.toRadians(mc.player.getYaw());
            double speed = flySpeed.get();
            double vx = -Math.sin(yawRad) * speed;
            double vz = Math.cos(yawRad) * speed;
            mc.player.setVelocity(vx, mc.player.getVelocity().y, vz);
        }
    }

    // Pathfinding: BFS through old chunks
    private List<BlockPos> findPathThroughOldChunks(BlockPos start, BlockPos goal) {
        Set<ChunkPos> oldChunks = Trails.getOldChunks();
        if (oldChunks == null || oldChunks.isEmpty()) return null;
        ChunkPos startChunk = new ChunkPos(start);
        ChunkPos goalChunk = new ChunkPos(goal);
        if (!oldChunks.contains(startChunk) || !oldChunks.contains(goalChunk)) {
            info("Start or goal is not in an old chunk.");
            return null;
        }
        // BFS
        Queue<ChunkPos> queue = new LinkedList<>();
        java.util.Map<ChunkPos, ChunkPos> cameFrom = new java.util.HashMap<>();
        Set<ChunkPos> visited = new HashSet<>();
        queue.add(startChunk);
        visited.add(startChunk);
        boolean found = false;
        while (!queue.isEmpty()) {
            ChunkPos current = queue.poll();
            if (current.equals(goalChunk)) {
                found = true;
                break;
            }
            for (ChunkPos neighbor : getNeighbors(current)) {
                if (oldChunks.contains(neighbor) && !visited.contains(neighbor)) {
                    queue.add(neighbor);
                    visited.add(neighbor);
                    cameFrom.put(neighbor, current);
                }
            }
        }
        if (!found) return null;
        // Reconstruct path
        List<BlockPos> path = new ArrayList<>();
        ChunkPos current = goalChunk;
        while (!current.equals(startChunk)) {
            path.add(chunkCenter(current, start.getY()));
            current = cameFrom.get(current);
        }
        Collections.reverse(path);
        return path;
    }

    // Get 4-way chunk neighbors
    private List<ChunkPos> getNeighbors(ChunkPos pos) {
        List<ChunkPos> neighbors = new ArrayList<>();
        neighbors.add(new ChunkPos(pos.x + 1, pos.z));
        neighbors.add(new ChunkPos(pos.x - 1, pos.z));
        neighbors.add(new ChunkPos(pos.x, pos.z + 1));
        neighbors.add(new ChunkPos(pos.x, pos.z - 1));
        return neighbors;
    }

    // Get center BlockPos of chunk at given Y
    private BlockPos chunkCenter(ChunkPos chunk, int y) {
        return new BlockPos(chunk.getCenterX(), y, chunk.getCenterZ());
    }

    // Set the waypoint path externally
    public void setWaypoints(List<BlockPos> path) {
        this.waypoints = path;
        this.currentWaypoint = 0;
        this.pathGenerated = false; // Allow regeneration if needed
    }

    // Gently descend and land, then disable elytra
    private void gentlyDescendAndLand() {
        if (mc.player == null) return;
        if (mc.player.isGliding()) {
            mc.player.setPitch(45f);
            mc.player.setVelocity(0, -0.2, 0);
            // If on ground, auto-disable elytra
            if (mc.player.isOnGround()) {
                mc.player.getInventory().armor.set(2, ItemStack.EMPTY); // Remove elytra from chest slot
                info("Landed and disabled elytra.");
            }
        }
    }

    // Check for obstacles in a straight line between player and target
    private boolean isObstacleInPath(Vec3d from, Vec3d to) {
        // Simple raycast (replace with better check if needed)
        int steps = 10;
        for (int i = 1; i <= steps; i++) {
            double t = i / (double) steps;
            double x = from.x + (to.x - from.x) * t;
            double y = from.y + (to.y - from.y) * t;
            double z = from.z + (to.z - from.z) * t;
            BlockPos pos = new BlockPos((int)x, (int)y, (int)z);
            if (!mc.world.getBlockState(pos).isAir()) return true;
        }
        return false;
    }

    // Check for enough fireworks
    private boolean hasEnoughFireworks() {
        return InvUtils.find(Items.FIREWORK_ROCKET).found();
    }

    // Check for good elytra durability (returns false if <10%)
    private boolean hasGoodElytra() {
        PlayerEntity player = mc.player;
        if (player == null) return false;
        ItemStack chest = player.getEquippedStack(EquipmentSlot.CHEST);
        if (!chest.isOf(Items.ELYTRA)) return false;
        // If your mappings have a method to check if the elytra is usable, add it here. Otherwise, skip this check.
        int max = chest.getMaxDamage();
        int left = max - chest.getDamage();
        return left > max * 0.1;
    }
    // Set the fly target externally (e.g. from UI or another module)
    public void setFlyTarget(BlockPos pos) {
        this.flyTarget = pos;
        this.pathGenerated = false; // Reset path if target changes
    }

    public void resetYLock() {
        yTarget = -1;
        launched = false;
        pathGenerated = false;
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        tickFlyLogic();
    }

    private void tryUseFirework() {
        FindItemResult hotbar = InvUtils.findInHotbar(Items.FIREWORK_ROCKET);
        if (!hotbar.found()) {
            FindItemResult inv = InvUtils.find(Items.FIREWORK_ROCKET);
            if (inv.found()) {
                int hotbarSlot = findEmptyHotbarSlot();
                if (hotbarSlot != -1) {
                    InvUtils.move().from(inv.slot()).to(hotbarSlot);
                } else {
                    info("No empty hotbar slot available to move fireworks.");
                    return;
                }
            } else {
                // Don't warn here, warn once in tickFlyLogic if needed
                return;
            }
        }

        if (mc.player.getMainHandStack().isOf(Items.FIREWORK_ROCKET)) {
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
        }

        lastRocketUse = System.currentTimeMillis();
    }

    private int findEmptyHotbarSlot() {
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).isEmpty()) return i;
        }
        return -1;
    }

    public enum AutoFireworkMode {
        VELOCITY,
        TIMED_DELAY
    }
}
