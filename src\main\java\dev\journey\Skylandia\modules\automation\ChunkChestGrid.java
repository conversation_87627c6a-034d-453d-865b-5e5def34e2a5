package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.render.Render2DEvent;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.item.*;
import net.minecraft.screen.slot.SlotActionType;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.*;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.Heightmap;

import java.util.*;

public class ChunkChestGrid extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgMovement = settings.createGroup("Movement");
    private final SettingGroup sgPlacement = settings.createGroup("Placement");
    private final SettingGroup sgShulker = settings.createGroup("Shulker Management");
    private final SettingGroup sgRender = settings.createGroup("Render");

    // Enums for better state management
    public enum GridState {
        INITIALIZING("Initializing"),
        MOVING_TO_CHUNK("Moving to Chunk"),
        PLACING_CHESTS("Placing Chests"),
        REFILLING_INVENTORY("Refilling"),
        COMPLETED("Completed"),
        ERROR("Error");

        private final String name;
        GridState(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    public enum ChestPlacementMode {
        RANDOM("Random"),
        GRID("Grid Pattern"),
        CORNERS("Chunk Corners"),
        CENTER("Center Focus");

        private final String name;
        ChestPlacementMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    // General settings
    private final Setting<Integer> gridSize = sgGeneral.add(new IntSetting.Builder()
        .name("grid-size")
        .description("Number of chunks in both forward and sideways directions.")
        .defaultValue(5)
        .min(1)
        .max(50)
        .sliderMax(20)
        .build());

    private final Setting<Integer> chestsPerChunk = sgGeneral.add(new IntSetting.Builder()
        .name("chests-per-chunk")
        .description("Number of chests to place per chunk.")
        .defaultValue(4)
        .min(1)
        .max(16)
        .sliderMax(10)
        .build());

    private final Setting<ChestPlacementMode> placementMode = sgGeneral.add(new EnumSetting.Builder<ChestPlacementMode>()
        .name("placement-mode")
        .description("How to arrange chests within each chunk.")
        .defaultValue(ChestPlacementMode.GRID)
        .build());

    private final Setting<Boolean> skipCompletedChunks = sgGeneral.add(new BoolSetting.Builder()
        .name("skip-completed")
        .description("Skip chunks that already have chests placed.")
        .defaultValue(true)
        .build());

    // Movement settings
    private final Setting<Boolean> useBaritone = sgMovement.add(new BoolSetting.Builder()
        .name("use-baritone")
        .description("Use Baritone for precise movement within chunks.")
        .defaultValue(true)
        .build());

    private final Setting<Direction> forwardDirection = sgMovement.add(new EnumSetting.Builder<Direction>()
        .name("forward-direction")
        .description("Direction to expand the grid forward.")
        .defaultValue(Direction.SOUTH)
        .build());

    private final Setting<Direction> sideDirection = sgMovement.add(new EnumSetting.Builder<Direction>()
        .name("side-direction")
        .description("Direction to expand the grid sideways.")
        .defaultValue(Direction.EAST)
        .build());

    private final Setting<Double> movementTimeout = sgMovement.add(new DoubleSetting.Builder()
        .name("movement-timeout")
        .description("Timeout for movement in seconds.")
        .defaultValue(30.0)
        .min(5.0)
        .max(120.0)
        .sliderMax(60.0)
        .build());

    // Placement settings
    private final Setting<Integer> placementDelay = sgPlacement.add(new IntSetting.Builder()
        .name("placement-delay")
        .description("Delay between chest placements in ticks.")
        .defaultValue(3)
        .min(1)
        .max(20)
        .sliderMax(10)
        .build());

    private final Setting<Boolean> groundOnly = sgPlacement.add(new BoolSetting.Builder()
        .name("ground-only")
        .description("Only place chests on solid ground.")
        .defaultValue(true)
        .build());

    private final Setting<Integer> yLevel = sgPlacement.add(new IntSetting.Builder()
        .name("y-level")
        .description("Y level offset from ground to place chests.")
        .defaultValue(1)
        .min(-5)
        .max(10)
        .sliderMax(5)
        .build());

    private final Setting<Integer> minSpacing = sgPlacement.add(new IntSetting.Builder()
        .name("min-spacing")
        .description("Minimum blocks between chest placements.")
        .defaultValue(3)
        .min(1)
        .max(8)
        .sliderMax(6)
        .build());

    // Shulker management settings
    private final Setting<Boolean> autoRefill = sgShulker.add(new BoolSetting.Builder()
        .name("auto-refill")
        .description("Automatically refill chests from shulker boxes.")
        .defaultValue(true)
        .build());

    private final Setting<Integer> refillThreshold = sgShulker.add(new IntSetting.Builder()
        .name("refill-threshold")
        .description("Refill when chest count falls below this number.")
        .defaultValue(8)
        .min(1)
        .max(64)
        .sliderMax(32)
        .visible(autoRefill::get)
        .build());

    private final Setting<Boolean> collectShulkerAfterUse = sgShulker.add(new BoolSetting.Builder()
        .name("collect-shulker")
        .description("Collect shulker box after emptying it.")
        .defaultValue(true)
        .visible(autoRefill::get)
        .build());

    // Render settings
    private final Setting<Boolean> renderChunkBounds = sgRender.add(new BoolSetting.Builder()
        .name("render-chunk-bounds")
        .description("Render chunk boundaries.")
        .defaultValue(true)
        .build());

    private final Setting<SettingColor> chunkBoundColor = sgRender.add(new ColorSetting.Builder()
        .name("chunk-bound-color")
        .description("Color for chunk boundaries.")
        .defaultValue(new SettingColor(255, 255, 255, 100))
        .visible(renderChunkBounds::get)
        .build());

    private final Setting<Boolean> renderPlannedChests = sgRender.add(new BoolSetting.Builder()
        .name("render-planned-chests")
        .description("Render planned chest positions.")
        .defaultValue(true)
        .build());

    private final Setting<SettingColor> plannedChestColor = sgRender.add(new ColorSetting.Builder()
        .name("planned-chest-color")
        .description("Color for planned chest positions.")
        .defaultValue(new SettingColor(0, 255, 0, 150))
        .visible(renderPlannedChests::get)
        .build());

    private final Setting<Boolean> renderPlacedChests = sgRender.add(new BoolSetting.Builder()
        .name("render-placed-chests")
        .description("Render successfully placed chests.")
        .defaultValue(true)
        .build());

    private final Setting<SettingColor> placedChestColor = sgRender.add(new ColorSetting.Builder()
        .name("placed-chest-color")
        .description("Color for placed chests.")
        .defaultValue(new SettingColor(0, 255, 255, 200))
        .visible(renderPlacedChests::get)
        .build());

    private final Setting<Boolean> renderCurrentChunk = sgRender.add(new BoolSetting.Builder()
        .name("render-current-chunk")
        .description("Highlight the current target chunk.")
        .defaultValue(true)
        .build());

    private final Setting<SettingColor> currentChunkColor = sgRender.add(new ColorSetting.Builder()
        .name("current-chunk-color")
        .description("Color for current target chunk.")
        .defaultValue(new SettingColor(255, 255, 0, 100))
        .visible(renderCurrentChunk::get)
        .build());

    // Enhanced state management
    private GridState currentState = GridState.INITIALIZING;
    private final List<ChunkPos> chunkGrid = new ArrayList<>();
    private final Map<ChunkPos, List<BlockPos>> plannedChestPositions = new HashMap<>();
    private final Map<ChunkPos, List<BlockPos>> placedChestPositions = new HashMap<>();
    private final Set<ChunkPos> completedChunks = new HashSet<>();
    
    private int currentChunkIndex = 0;
    private int currentChestIndex = 0;
    private ChunkPos currentTargetChunk = null;
    private List<BlockPos> currentChunkChestPositions = new ArrayList<>();
    
    // Movement and timing
    private long movementStartTime = 0;
    private long lastPlacementTime = 0;
    private BlockPos targetPosition = null;
    private boolean isMovingToPosition = false;
    
    // Shulker management
    private boolean refillingFromShulker = false;
    private BlockPos shulkerPosition = null;
    private int shulkerRefillAttempts = 0;
    private static final int MAX_SHULKER_ATTEMPTS = 10;

    public ChunkChestGrid() {
        super(Skylandia.Automation, "chunk-chest-grid", 
            "Advanced chunk-based chest placement system with visual feedback and Baritone integration.");
    }

    @Override
    public void onActivate() {
        if (mc.player == null || mc.world == null) {
            error("Player or world is null!");
            toggle();
            return;
        }

        // Initialize state
        currentState = GridState.INITIALIZING;
        chunkGrid.clear();
        plannedChestPositions.clear();
        placedChestPositions.clear();
        completedChunks.clear();
        currentChunkIndex = 0;
        currentChestIndex = 0;
        currentTargetChunk = null;
        currentChunkChestPositions.clear();
        movementStartTime = 0;
        lastPlacementTime = 0;
        targetPosition = null;
        isMovingToPosition = false;
        refillingFromShulker = false;
        shulkerPosition = null;
        shulkerRefillAttempts = 0;

        // Generate chunk grid
        generateChunkGrid();
        
        // Pre-calculate all chest positions
        generateAllChestPositions();
        
        info("Initialized %d x %d chunk grid (%d chunks, %d total chests)", 
            gridSize.get(), gridSize.get(), chunkGrid.size(), 
            plannedChestPositions.values().stream().mapToInt(List::size).sum());
        
        currentState = GridState.MOVING_TO_CHUNK;
    }

    @Override
    public void onDeactivate() {
        // Clean shutdown
        stopBaritone();
        currentState = GridState.COMPLETED;
        
        // Clear all state
        chunkGrid.clear();
        plannedChestPositions.clear();
        placedChestPositions.clear();
        completedChunks.clear();
        currentChunkChestPositions.clear();
        
        info("Chunk chest grid automation stopped - Placed %d chests in %d chunks", 
            placedChestPositions.values().stream().mapToInt(List::size).sum(),
            completedChunks.size());
    }

    private void generateChunkGrid() {
        ChunkPos startChunk = mc.player.getChunkPos();
        
        for (int row = 0; row < gridSize.get(); row++) {
            for (int col = 0; col < gridSize.get(); col++) {
                int dx = forwardDirection.get().getOffsetX() * row + sideDirection.get().getOffsetX() * col;
                int dz = forwardDirection.get().getOffsetZ() * row + sideDirection.get().getOffsetZ() * col;
                ChunkPos chunkPos = new ChunkPos(startChunk.x + dx, startChunk.z + dz);
                chunkGrid.add(chunkPos);
            }
        }
    }

    private void generateAllChestPositions() {
        for (ChunkPos chunk : chunkGrid) {
            List<BlockPos> positions = generateChestPositionsForChunk(chunk);
            plannedChestPositions.put(chunk, positions);
            placedChestPositions.put(chunk, new ArrayList<>());
        }
    }

    private List<BlockPos> generateChestPositionsForChunk(ChunkPos chunk) {
        List<BlockPos> positions = new ArrayList<>();
        int chestsToPlace = chestsPerChunk.get();
        
        switch (placementMode.get()) {
            case GRID -> {
                // Create a grid pattern within the chunk
                int gridDimension = (int) Math.ceil(Math.sqrt(chestsToPlace));
                int spacing = Math.max(minSpacing.get(), 16 / (gridDimension + 1));
                
                for (int i = 0; i < gridDimension && positions.size() < chestsToPlace; i++) {
                    for (int j = 0; j < gridDimension && positions.size() < chestsToPlace; j++) {
                        int x = chunk.getStartX() + 2 + (i * spacing);
                        int z = chunk.getStartZ() + 2 + (j * spacing);
                        if (x < chunk.getStartX() + 16 && z < chunk.getStartZ() + 16) {
                            positions.add(new BlockPos(x, 0, z)); // Y will be calculated later
                        }
                    }
                }
            }
            case CORNERS -> {
                // Place chests in chunk corners and center
                positions.add(new BlockPos(chunk.getStartX() + 2, 0, chunk.getStartZ() + 2));
                positions.add(new BlockPos(chunk.getStartX() + 14, 0, chunk.getStartZ() + 2));
                positions.add(new BlockPos(chunk.getStartX() + 2, 0, chunk.getStartZ() + 14));
                positions.add(new BlockPos(chunk.getStartX() + 14, 0, chunk.getStartZ() + 14));
                if (chestsToPlace > 4) {
                    positions.add(new BlockPos(chunk.getStartX() + 8, 0, chunk.getStartZ() + 8));
                }
            }
            case CENTER -> {
                // Focus around chunk center
                int centerX = chunk.getStartX() + 8;
                int centerZ = chunk.getStartZ() + 8;
                positions.add(new BlockPos(centerX, 0, centerZ));
                
                int[] offsets = {-3, 0, 3};
                for (int dx : offsets) {
                    for (int dz : offsets) {
                        if (positions.size() >= chestsToPlace) break;
                        if (dx == 0 && dz == 0) continue; // Skip center, already added
                        positions.add(new BlockPos(centerX + dx, 0, centerZ + dz));
                    }
                }
            }
            case RANDOM -> {
                // Random positions with minimum spacing
                Random random = new Random(chunk.hashCode()); // Consistent random based on chunk
                for (int attempt = 0; attempt < 100 && positions.size() < chestsToPlace; attempt++) {
                    int x = chunk.getStartX() + 2 + random.nextInt(12);
                    int z = chunk.getStartZ() + 2 + random.nextInt(12);
                    BlockPos candidate = new BlockPos(x, 0, z);
                    
                    boolean tooClose = false;
                    for (BlockPos existing : positions) {
                        if (candidate.isWithinDistance(existing, minSpacing.get())) {
                            tooClose = true;
                            break;
                        }
                    }
                    
                    if (!tooClose) {
                        positions.add(candidate);
                    }
                }
            }
        }
        
        return positions;
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;
        
        // Handle different states
        switch (currentState) {
            case INITIALIZING -> {
                // Should not reach here after onActivate
                currentState = GridState.MOVING_TO_CHUNK;
            }
            
            case MOVING_TO_CHUNK -> {
                handleMovingToChunk();
            }
            
            case PLACING_CHESTS -> {
                handlePlacingChests();
            }
            
            case REFILLING_INVENTORY -> {
                handleRefilling();
            }
            
            case COMPLETED -> {
                info("All chunks completed! Disabling module.");
                toggle();
            }
            
            case ERROR -> {
                error("Module encountered an error. Disabling.");
                toggle();
            }
        }
    }

    private void handleMovingToChunk() {
        // Check if we've completed all chunks
        if (currentChunkIndex >= chunkGrid.size()) {
            currentState = GridState.COMPLETED;
            return;
        }

        // Get current target chunk
        currentTargetChunk = chunkGrid.get(currentChunkIndex);
        
        // Skip if chunk is already completed and setting is enabled
        if (skipCompletedChunks.get() && completedChunks.contains(currentTargetChunk)) {
            currentChunkIndex++;
            return;
        }

        // Check if we need to refill inventory first
        if (!hasEnoughChests()) {
            if (autoRefill.get()) {
                currentState = GridState.REFILLING_INVENTORY;
                return;
            } else {
                error("Not enough chests in inventory and auto-refill is disabled!");
                currentState = GridState.ERROR;
                return;
            }
        }

        // Check if we're already in the target chunk
        if (isInTargetChunk(currentTargetChunk)) {
            // We're in the target chunk, start placing chests
            currentChunkChestPositions = new ArrayList<>(plannedChestPositions.get(currentTargetChunk));
            currentChestIndex = 0;
            currentState = GridState.PLACING_CHESTS;
            info("Entered chunk (%d, %d) - placing %d chests", 
                currentTargetChunk.x, currentTargetChunk.z, currentChunkChestPositions.size());
            return;
        }

        // Need to move to target chunk
        if (!isMovingToPosition) {
            startMovementToChunk(currentTargetChunk);
        } else {
            // Check movement timeout
            if (System.currentTimeMillis() - movementStartTime > movementTimeout.get() * 1000) {
                warning("Movement timeout reached for chunk (%d, %d)", currentTargetChunk.x, currentTargetChunk.z);
                currentChunkIndex++; // Skip this chunk
                isMovingToPosition = false;
            }
        }
    }

    private void handlePlacingChests() {
        // Check if all chests in current chunk are placed
        if (currentChestIndex >= currentChunkChestPositions.size()) {
            // Mark chunk as completed
            completedChunks.add(currentTargetChunk);
            currentChunkIndex++;
            currentState = GridState.MOVING_TO_CHUNK;
            isMovingToPosition = false;
            
            info("Completed chunk (%d, %d) - %d/%d chests placed", 
                currentTargetChunk.x, currentTargetChunk.z,
                placedChestPositions.get(currentTargetChunk).size(),
                currentChunkChestPositions.size());
            return;
        }

        // Check placement delay
        if (System.currentTimeMillis() - lastPlacementTime < placementDelay.get() * 50) {
            return;
        }

        // Get next chest position to place
        BlockPos chestPos = currentChunkChestPositions.get(currentChestIndex);
        BlockPos validPos = findValidPositionNear(chestPos);
        
        if (validPos == null) {
            warning("Could not find valid position for chest at %s", chestPos.toString());
            currentChestIndex++;
            return;
        }

        // Move to chest position if needed
        if (!isInPlacementRange(validPos)) {
            if (useBaritone.get()) {
                navigateToPosition(validPos);
            }
            return;
        }

        // Place the chest
        if (placeChestAt(validPos)) {
            placedChestPositions.get(currentTargetChunk).add(validPos);
            currentChestIndex++;
            lastPlacementTime = System.currentTimeMillis();
            
            info("Placed chest %d/%d in chunk (%d, %d) at %s", 
                currentChestIndex, currentChunkChestPositions.size(),
                currentTargetChunk.x, currentTargetChunk.z, validPos.toString());
        } else {
            // Failed to place, try next position
            currentChestIndex++;
        }
    }

    private void handleRefilling() {
        if (refillingFromShulker) {
            handleShulkerRefill();
        } else {
            startShulkerRefill();
        }
    }

    private boolean hasEnoughChests() {
        FindItemResult chestResult = InvUtils.find(Items.CHEST);
        return chestResult.found() && chestResult.count() >= refillThreshold.get();
    }

    private boolean isInTargetChunk(ChunkPos targetChunk) {
        ChunkPos playerChunk = mc.player.getChunkPos();
        return playerChunk.x == targetChunk.x && playerChunk.z == targetChunk.z;
    }

    private boolean isInPlacementRange(BlockPos pos) {
        return mc.player.getBlockPos().isWithinDistance(pos, 5);
    }

    private void startMovementToChunk(ChunkPos targetChunk) {
        // Calculate chunk center
        int centerX = targetChunk.getStartX() + 8;
        int centerZ = targetChunk.getStartZ() + 8;
        int y = getGroundLevel(centerX, centerZ);
        
        targetPosition = new BlockPos(centerX, y, centerZ);
        
        if (useBaritone.get()) {
            navigateToPosition(targetPosition);
        }
        
        isMovingToPosition = true;
        movementStartTime = System.currentTimeMillis();
        
        info("Moving to chunk (%d, %d) at position %s", 
            targetChunk.x, targetChunk.z, targetPosition.toString());
    }

    private void navigateToPosition(BlockPos pos) {
        try {
            Class<?> baritoneClass = Class.forName("baritone.api.BaritoneAPI");
            Object baritone = baritoneClass.getMethod("getProvider").invoke(null);
            Object primaryBaritone = baritone.getClass().getMethod("getPrimaryBaritone").invoke(baritone);
            Object pathingBehavior = primaryBaritone.getClass().getMethod("getPathingBehavior").invoke(primaryBaritone);
            
            // Cancel current path and set new goal
            pathingBehavior.getClass().getMethod("cancelEverything").invoke(pathingBehavior);
            
            Object commandManager = primaryBaritone.getClass().getMethod("getCommandManager").invoke(primaryBaritone);
            String command = String.format("goto %d %d %d", pos.getX(), pos.getY(), pos.getZ());
            commandManager.getClass().getMethod("execute", String.class).invoke(commandManager, command);
            
        } catch (Exception e) {
            warning("Baritone not available: %s", e.getMessage());
        }
    }

    private void stopBaritone() {
        try {
            Class<?> baritoneClass = Class.forName("baritone.api.BaritoneAPI");
            Object baritone = baritoneClass.getMethod("getProvider").invoke(null);
            Object primaryBaritone = baritone.getClass().getMethod("getPrimaryBaritone").invoke(baritone);
            Object pathingBehavior = primaryBaritone.getClass().getMethod("getPathingBehavior").invoke(primaryBaritone);
            pathingBehavior.getClass().getMethod("cancelEverything").invoke(pathingBehavior);
        } catch (Exception e) {
            // Baritone not available, ignore
        }
    }

    private int getGroundLevel(int x, int z) {
        int y = mc.world.getTopY(Heightmap.Type.WORLD_SURFACE, x, z);
        
        // Find actual ground level
        for (int checkY = y; checkY > mc.world.getBottomY(); checkY--) {
            BlockPos checkPos = new BlockPos(x, checkY, z);
            if (!mc.world.getBlockState(checkPos).isAir()) {
                return checkY + 1;
            }
        }
        
        return y;
    }

    private BlockPos findValidPositionNear(BlockPos target) {
        // First try the exact position
        BlockPos validPos = findValidChestPosition(target.getX(), target.getZ());
        if (validPos != null) return validPos;
        
        // Try nearby positions in expanding radius
        for (int radius = 1; radius <= 3; radius++) {
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    if (Math.abs(dx) == radius || Math.abs(dz) == radius) {
                        validPos = findValidChestPosition(target.getX() + dx, target.getZ() + dz);
                        if (validPos != null) return validPos;
                    }
                }
            }
        }
        
        return null;
    }

    private BlockPos findValidChestPosition(int x, int z) {
        int groundY = getGroundLevel(x, z);
        BlockPos chestPos = new BlockPos(x, groundY + yLevel.get(), z);
        
        // Check if position is valid
        if (!mc.world.getBlockState(chestPos).isAir()) return null;
        if (!mc.world.getBlockState(chestPos.up()).isAir()) return null;
        
        if (groundOnly.get()) {
            if (!mc.world.getBlockState(chestPos.down()).isSolidBlock(mc.world, chestPos.down())) {
                return null;
            }
        }
        
        // Check protected regions
        if (Skylandia.isProtectedRegion(chestPos)) return null;
        
        // Check minimum spacing with already placed chests
        ChunkPos chunkPos = new ChunkPos(chestPos);
        List<BlockPos> placedInChunk = placedChestPositions.get(chunkPos);
        if (placedInChunk != null) {
            for (BlockPos placed : placedInChunk) {
                if (chestPos.isWithinDistance(placed, minSpacing.get())) {
                    return null;
                }
            }
        }
        
        return chestPos;
    }

    private boolean placeChestAt(BlockPos pos) {
        // Find chest in inventory
        FindItemResult chestResult = InvUtils.find(Items.CHEST);
        if (!chestResult.found()) {
            error("No chest found in inventory!");
            return false;
        }

        // Switch to chest
        if (chestResult.getHand() == null) {
            InvUtils.swap(chestResult.slot(), false);
        }

        // Place chest
        BlockHitResult hitResult = new BlockHitResult(
            Vec3d.ofCenter(pos),
            Direction.UP,
            pos.down(),
            false
        );

        boolean success = mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, hitResult).isAccepted();
        if (success) {
            mc.player.swingHand(Hand.MAIN_HAND);
        }

        return success;
    }

    private void startShulkerRefill() {
        if (shulkerRefillAttempts >= MAX_SHULKER_ATTEMPTS) {
            error("Max shulker refill attempts reached!");
            currentState = GridState.ERROR;
            return;
        }

        // Find shulker box in inventory
        FindItemResult shulkerResult = InvUtils.find(itemStack -> 
            itemStack.getItem() instanceof BlockItem blockItem && 
            blockItem.getBlock().toString().contains("shulker_box"));

        if (!shulkerResult.found()) {
            error("No shulker boxes found in inventory!");
            currentState = GridState.ERROR;
            return;
        }

        // Find clear position for shulker
        BlockPos playerPos = mc.player.getBlockPos();
        BlockPos shulkerPos = findClearShulkerPosition(playerPos);
        
        if (shulkerPos == null) {
            error("Cannot find clear space to place shulker box!");
            return;
        }

        // Switch to shulker box
        if (shulkerResult.getHand() == null) {
            InvUtils.swap(shulkerResult.slot(), false);
        }

        // Place shulker
        BlockHitResult hitResult = new BlockHitResult(
            Vec3d.ofCenter(shulkerPos),
            Direction.UP,
            shulkerPos.down(),
            false
        );

        if (mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, hitResult).isAccepted()) {
            mc.player.swingHand(Hand.MAIN_HAND);
            shulkerPosition = shulkerPos;
            refillingFromShulker = true;
            shulkerRefillAttempts++;
            info("Placed shulker box for refilling at %s (attempt %d)", 
                shulkerPos.toString(), shulkerRefillAttempts);
        } else {
            warning("Failed to place shulker box at %s", shulkerPos.toString());
        }
    }

    private BlockPos findClearShulkerPosition(BlockPos playerPos) {
        BlockPos[] candidates = {
            playerPos.up(),
            playerPos.add(1, 0, 0),
            playerPos.add(-1, 0, 0),
            playerPos.add(0, 0, 1),
            playerPos.add(0, 0, -1),
            playerPos.add(1, 1, 0),
            playerPos.add(-1, 1, 0),
            playerPos.add(0, 1, 1),
            playerPos.add(0, 1, -1)
        };

        for (BlockPos pos : candidates) {
            if (mc.world.getBlockState(pos).isAir() && 
                mc.world.getBlockState(pos.up()).isAir()) {
                return pos;
            }
        }

        return null;
    }

    private void handleShulkerRefill() {
        if (shulkerPosition == null) {
            refillingFromShulker = false;
            currentState = GridState.MOVING_TO_CHUNK;
            return;
        }

        if (mc.currentScreen != null) {
            // GUI is open, transfer chests
            transferChestsFromShulker();
        } else {
            // No GUI, try to interact with shulker
            if (mc.world.getBlockState(shulkerPosition).getBlock().toString().contains("shulker_box")) {
                // Try to open shulker
                BlockHitResult hitResult = new BlockHitResult(
                    Vec3d.ofCenter(shulkerPosition),
                    Direction.UP,
                    shulkerPosition,
                    false
                );
                
                if (!mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, hitResult).isAccepted()) {
                    // If we can't open it, break it and collect items
                    breakAndCollectShulker();
                }
            } else {
                // Shulker is gone, finish refill
                finishShulkerRefill();
            }
        }
    }

    private void transferChestsFromShulker() {
        if (mc.player.currentScreenHandler == null) return;

        boolean transferredAny = false;
        for (int i = 0; i < mc.player.currentScreenHandler.slots.size(); i++) {
            ItemStack stack = mc.player.currentScreenHandler.getSlot(i).getStack();
            if (stack.getItem() == Items.CHEST) {
                mc.interactionManager.clickSlot(
                    mc.player.currentScreenHandler.syncId, 
                    i, 
                    0, 
                    SlotActionType.QUICK_MOVE, 
                    mc.player
                );
                info("Transferred %d chests from shulker", stack.getCount());
                transferredAny = true;
                break; // Transfer one stack at a time
            }
        }

        // If we have enough chests or nothing more to transfer, close and collect shulker
        if (!transferredAny || hasEnoughChests()) {
            mc.player.closeScreen();
            if (collectShulkerAfterUse.get()) {
                breakAndCollectShulker();
            } else {
                finishShulkerRefill();
            }
        }
    }

    private void breakAndCollectShulker() {
        mc.interactionManager.attackBlock(shulkerPosition, Direction.UP);
        mc.player.swingHand(Hand.MAIN_HAND);
        info("Breaking shulker box to collect items");
        
        // Wait a moment for items to drop, then finish
        finishShulkerRefill();
    }

    private void finishShulkerRefill() {
        refillingFromShulker = false;
        shulkerPosition = null;
        shulkerRefillAttempts = 0;
        currentState = GridState.MOVING_TO_CHUNK;
        info("Shulker refill completed");
    }

    // Rendering methods
    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;

        // Render chunk boundaries
        if (renderChunkBounds.get()) {
            renderChunkBoundaries(event);
        }

        // Render current target chunk
        if (renderCurrentChunk.get() && currentTargetChunk != null) {
            renderCurrentTargetChunk(event);
        }

        // Render planned chest positions
        if (renderPlannedChests.get()) {
            renderPlannedChestPositions(event);
        }

        // Render placed chests
        if (renderPlacedChests.get()) {
            renderPlacedChestPositions(event);
        }
    }

    private void renderChunkBoundaries(Render3DEvent event) {
        for (ChunkPos chunk : chunkGrid) {
            int startX = chunk.getStartX();
            int startZ = chunk.getStartZ();
            int endX = startX + 16;
            int endZ = startZ + 16;
            
            int y1 = mc.player.getBlockY() - 5;
            int y2 = mc.player.getBlockY() + 10;

            // Render chunk boundary lines
            event.renderer.line(startX, y1, startZ, startX, y2, startZ, chunkBoundColor.get());
            event.renderer.line(endX, y1, startZ, endX, y2, startZ, chunkBoundColor.get());
            event.renderer.line(startX, y1, endZ, startX, y2, endZ, chunkBoundColor.get());
            event.renderer.line(endX, y1, endZ, endX, y2, endZ, chunkBoundColor.get());
        }
    }

    private void renderCurrentTargetChunk(Render3DEvent event) {
        if (currentTargetChunk == null) return;

        int startX = currentTargetChunk.getStartX();
        int startZ = currentTargetChunk.getStartZ();
        int endX = startX + 16;
        int endZ = startZ + 16;
        
        int y = mc.player.getBlockY();
        
        // Render highlighted chunk boundary
        Box chunkBox = new Box(startX, y - 2, startZ, endX, y + 5, endZ);
        event.renderer.box(chunkBox, currentChunkColor.get(), currentChunkColor.get(), ShapeMode.Lines, 0);
    }

    private void renderPlannedChestPositions(Render3DEvent event) {
        if (currentTargetChunk == null) return;
        
        List<BlockPos> planned = plannedChestPositions.get(currentTargetChunk);
        if (planned == null) return;

        for (BlockPos pos : planned) {
            int y = getGroundLevel(pos.getX(), pos.getZ()) + yLevel.get();
            BlockPos renderPos = new BlockPos(pos.getX(), y, pos.getZ());
            event.renderer.box(renderPos, plannedChestColor.get(), plannedChestColor.get(), ShapeMode.Both, 0);
        }
    }

    private void renderPlacedChestPositions(Render3DEvent event) {
        for (Map.Entry<ChunkPos, List<BlockPos>> entry : placedChestPositions.entrySet()) {
            for (BlockPos pos : entry.getValue()) {
                event.renderer.box(pos, placedChestColor.get(), placedChestColor.get(), ShapeMode.Lines, 0);
            }
        }
    }

    @EventHandler
    private void onRender2D(Render2DEvent event) {
        if (mc.player == null || mc.world == null) return;
        
        TextRenderer textRenderer = TextRenderer.get();
        int y = 10;
        
        textRenderer.begin(1, false, true);
        
        // Module status
        textRenderer.render(String.format("ChunkChestGrid [%s]", currentState.toString()), 
                          10, y, Color.WHITE);
        y += 12;
        
        // Progress info
        if (!chunkGrid.isEmpty()) {
            textRenderer.render(String.format("Progress: %d/%d chunks (%d%% complete)", 
                              completedChunks.size(), chunkGrid.size(),
                              (completedChunks.size() * 100) / chunkGrid.size()), 10, y, Color.GREEN);
            y += 12;
        }
        
        // Current chunk info
        if (currentTargetChunk != null) {
            textRenderer.render(String.format("Current Chunk: (%d, %d)", 
                              currentTargetChunk.x, currentTargetChunk.z), 10, y, Color.YELLOW);
            y += 12;
            
            List<BlockPos> placed = placedChestPositions.get(currentTargetChunk);
            List<BlockPos> planned = plannedChestPositions.get(currentTargetChunk);
            if (placed != null && planned != null) {
                textRenderer.render(String.format("Chests: %d/%d placed", 
                                  placed.size(), planned.size()), 10, y, Color.CYAN);
                y += 12;
            }
        }
        
        // Inventory info
        FindItemResult chestResult = InvUtils.find(Items.CHEST);
        Color chestColor = chestResult.count() >= refillThreshold.get() ? Color.GREEN : Color.RED;
        textRenderer.render(String.format("Chests in inventory: %d", chestResult.count()), 
                          10, y, chestColor);
        y += 12;
        
        // Shulker refill info
        if (refillingFromShulker) {
            textRenderer.render(String.format("Refilling from shulker... (attempt %d)", 
                              shulkerRefillAttempts), 10, y, Color.ORANGE);
        }
        
        textRenderer.end();
    }

    @Override
    public String getInfoString() {
        if (chunkGrid.isEmpty()) return "Initializing";
        return String.format("%d/%d chunks (%s)", 
            completedChunks.size(), chunkGrid.size(), currentState.toString());
    }
}
