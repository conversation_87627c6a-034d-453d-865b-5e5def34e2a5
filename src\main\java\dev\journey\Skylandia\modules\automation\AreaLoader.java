package dev.journey.Skylandia.modules.automation;

import baritone.api.BaritoneAPI;
import baritone.api.process.IElytraProcess;
import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.ChatUtils;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import net.minecraft.item.ItemStack;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.Items;
import net.minecraft.util.math.ChunkPos;
import java.util.*;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import net.minecraft.util.math.Vec3d;
import java.util.List;
import java.util.ArrayList;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.dimension.DimensionType;


public class AreaLoader extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final Setting<Integer> gapDistance = sgGeneral.add(new IntSetting.Builder()
            .name("gap-distance")
            .description("Gap in chunks between each line of the spiral.")
            .defaultValue(16)
            .min(5)
            .sliderMax(256)
            .build()
    );
    private final Setting<Integer> flightLevel = sgGeneral.add(new IntSetting.Builder()
            .name("flight-level")
            .description("What y-level to (roughly) fly at. It will still navigate around terrain as necessary.")
            .defaultValue(180)
            .min(80)
            .sliderMax(255)
            .build()
    );
    // New: Enable DemonCrystal-style movement
    private final Setting<Boolean> enableDirectFly = sgGeneral.add(new BoolSetting.Builder()
            .name("Enable Direct Fly")
            .description("Use velocity-based movement to fly to each spiral destination instead of Baritone.")
            .defaultValue(false)
            .build()
    );
    private final Setting<Double> flySpeed = sgGeneral.add(new DoubleSetting.Builder()
            .name("Direct Fly Speed")
            .description("Speed to use when flying toward the destination block.")
            .defaultValue(0.7)
            .min(0.1)
            .sliderMax(2.0)
            .visible(enableDirectFly::get)
            .build()
    );
    // Pattern selection
    public enum PatternMode { SPIRAL, GRID, PERIMETER, RANDOM }
    private final Setting<PatternMode> patternMode = sgGeneral.add(new EnumSetting.Builder<PatternMode>()
            .name("Pattern Mode")
            .description("Choose the chunk loading pattern.")
            .defaultValue(PatternMode.SPIRAL)
            .build()
    );
    private final Setting<Boolean> enableChunkFeedback = sgGeneral.add(new BoolSetting.Builder()
            .name("Chunk Status Feedback")
            .description("Visualize loaded chunks and skip already-loaded ones.")
            .defaultValue(false)
            .build()
    );
    private final Setting<Boolean> enableResourceMonitor = sgGeneral.add(new BoolSetting.Builder()
            .name("Resource Monitor")
            .description("Pause or return if fireworks or elytra are low.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> enablePlayerDetect = sgGeneral.add(new BoolSetting.Builder()
            .name("Pause on Player Detection")
            .description("Pause if another player is nearby.")
            .defaultValue(false)
            .build()
    );
    private final Setting<Boolean> enableAutoReturn = sgGeneral.add(new BoolSetting.Builder()
            .name("Auto-Return Home")
            .description("Return to start/home after loading.")
            .defaultValue(false)
            .build()
    );
    private final Setting<Boolean> useAFKVanillaFly = sgGeneral.add(new BoolSetting.Builder()
            .name("Use AFKVanillaFly Logic")
            .description("Use AFKVanillaFly's advanced flight logic for chunk loading.")
            .defaultValue(false)
            .build()
    );
    private final int[][] multipliers = {
            {0, 1}, {1, 0}, {0, -1}, {-1, 0}
    };
    private IElytraProcess efly;
    private int sideDistance = 0;
    private int multiIndex = 0;
    private BlockPos lastDest = null;
    private RegistryEntry<DimensionType> dimension;
    private boolean dimensionChanged = false;
    private BlockPos startPos = null;
    private Set<ChunkPos> loadedChunks = new HashSet<>();
    private Random random = new Random();

    public AreaLoader() {
        super(Skylandia.Hunting, "area-loader", "Spiral out from your position to load chunks in an area");
    }

    private void reset() {
        multiIndex = 0;
        sideDistance = 16 * gapDistance.get();
        efly = BaritoneAPI.getProvider().getPrimaryBaritone().getElytraProcess();
        startPos = mc.player != null ? mc.player.getBlockPos() : null;
        loadedChunks.clear();
    }

    @EventHandler
    public void onTick(TickEvent.Post event) {
        // Resource monitoring
        if (enableResourceMonitor.get() && (!hasEnoughFireworks() || !hasGoodElytra())) {
            ChatUtils.sendMsg("area-loader", Text.literal("Low fireworks or elytra! Pausing."));
            this.toggle();
            return;
        }
        // Player detection
        if (enablePlayerDetect.get() && isPlayerNearby()) {
            ChatUtils.sendMsg("area-loader", Text.literal("Player detected nearby! Pausing."));
            this.toggle();
            return;
        }
        // Mark current chunk as loaded
        if (mc.player != null) loadedChunks.add(new ChunkPos(mc.player.getBlockPos()));
        // Pattern logic
        BlockPos nextDest = null;
        switch (patternMode.get()) {
            case GRID:
                nextDest = getNextGridDestination();
                break;
            case PERIMETER:
                nextDest = getNextPerimeterDestination();
                break;
            case RANDOM:
                nextDest = getNextRandomDestination();
                break;
            case SPIRAL:
            default:
                nextDest = getNextDestination(lastDest == null ? startPos : lastDest);
                break;
        }
        if (nextDest == null) return;
        lastDest = nextDest;
        // Use AFKVanillaFly logic if enabled
        if (useAFKVanillaFly.get()) {
            // Set AFKVanillaFly waypoints and enable
            AFKVanillaFly afk = (AFKVanillaFly) meteordevelopment.meteorclient.systems.modules.Modules.get().get(AFKVanillaFly.class);
            if (afk != null) {
                afk.setWaypoints(Collections.singletonList(nextDest));
                afk.toggle();
            }
        } else if (enableDirectFly.get()) {
            // Direct velocity-based movement
            if (mc.player == null || !mc.player.isGliding()) return;
            Vec3d playerPos = mc.player.getPos();
            Vec3d targetCenter = new Vec3d(nextDest.getX() + 0.5, flightLevel.get(), nextDest.getZ() + 0.5);
            double dx = targetCenter.x - playerPos.x;
            double dz = targetCenter.z - playerPos.z;
            double dist = Math.sqrt(dx * dx + dz * dz);
            if (dist < 5.0) {
                // Arrived, mark chunk and get next
                loadedChunks.add(new ChunkPos(nextDest));
            } else {
                double speed = flySpeed.get();
                double vx = (dx / dist) * speed;
                double vz = (dz / dist) * speed;
                mc.player.setVelocity(vx, mc.player.getVelocity().y, vz);
            }
        } else {
            // Baritone ElytraProcess logic (original)
            if (efly == null || !efly.isLoaded()) {
                this.toggle();
                return;
            }
            if (mc.world.getDimensionEntry() != dimension) {
                if (dimensionChanged) return;
                dimensionChanged = true;
                ChatUtils.sendMsg("area-loader", Text.literal("Dimension changed, pausing."));
                return;
            } else if (dimensionChanged) {
                dimensionChanged = false;
                ChatUtils.sendMsg("area-loader", Text.literal("Resuming."));
                efly.pathTo(lastDest);
            }
            var pos = mc.player.getBlockPos();
            var dest = efly.currentDestination();
            if (dest == null) return;
            var deltaX = Math.abs(pos.getX() - dest.getX());
            var deltaY = Math.abs(pos.getZ() - dest.getZ());
            if (deltaX < 60 && deltaY < 60) {
                lastDest = getNextDestination(dest);
                efly.pathTo(lastDest);
            }
        }
        // Auto-return
        if (enableAutoReturn.get() && isPatternComplete()) {
            if (startPos != null) {
                lastDest = startPos;
                ChatUtils.sendMsg("area-loader", Text.literal("Returning to start/home."));
            }
        }
        // Chunk feedback (visualization placeholder)
        if (enableChunkFeedback.get()) {
            // Print loaded chunk count
            ChatUtils.sendMsg("area-loader", Text.literal("Chunks loaded: " + loadedChunks.size()));
        }
    }
    // Check for enough fireworks
    private boolean hasEnoughFireworks() {
        return InvUtils.find(Items.FIREWORK_ROCKET).found();
    }
    // Check for good elytra durability (returns false if <10%)
    private boolean hasGoodElytra() {
        PlayerEntity player = mc.player;
        if (player == null) return false;
        ItemStack chest = player.getEquippedStack(EquipmentSlot.CHEST);
        if (!chest.isOf(Items.ELYTRA)) return false;
        // If your mappings have a method to check if the elytra is usable, add it here. Otherwise, skip this check.
        int max = chest.getMaxDamage();
        int left = max - chest.getDamage();
        return left > max * 0.1;
    }
    // Check for nearby players (within 32 blocks)
    private boolean isPlayerNearby() {
        if (mc.world == null || mc.player == null) return false;
        for (PlayerEntity p : mc.world.getPlayers()) {
            if (p != mc.player && p.squaredDistanceTo(mc.player) < 32 * 32) return true;
        }
        return false;
    }

    // GRID pattern: move in a grid, skipping loaded chunks
    private BlockPos getNextGridDestination() {
        if (startPos == null) return null;
        int range = 8; // 8 chunks in each direction
        for (int dx = -range; dx <= range; dx++) {
            for (int dz = -range; dz <= range; dz++) {
                ChunkPos cp = new ChunkPos(startPos.getX() / 16 + dx, startPos.getZ() / 16 + dz);
                if (!loadedChunks.contains(cp)) {
                    return new BlockPos(cp.getStartX(), flightLevel.get(), cp.getStartZ());
                }
            }
        }
        return null;
    }
    // PERIMETER pattern: walk the edge of a square
    private BlockPos getNextPerimeterDestination() {
        if (startPos == null) return null;
        int radius = 8;
        int px = startPos.getX() / 16;
        int pz = startPos.getZ() / 16;
        for (int i = 0; i < 4 * radius; i++) {
            int x = px + (i < radius ? i : i < 2 * radius ? radius : i < 3 * radius ? 3 * radius - i : 0);
            int z = pz + (i < radius ? 0 : i < 2 * radius ? i - radius : i < 3 * radius ? radius : 4 * radius - i);
            ChunkPos cp = new ChunkPos(x, z);
            if (!loadedChunks.contains(cp)) {
                return new BlockPos(cp.getStartX(), flightLevel.get(), cp.getStartZ());
            }
        }
        return null;
    }
    // RANDOM pattern: pick a random chunk in range
    private BlockPos getNextRandomDestination() {
        if (startPos == null) return null;
        int range = 8;
        for (int tries = 0; tries < 100; tries++) {
            int dx = random.nextInt(-range, range + 1);
            int dz = random.nextInt(-range, range + 1);
            ChunkPos cp = new ChunkPos(startPos.getX() / 16 + dx, startPos.getZ() / 16 + dz);
            if (!loadedChunks.contains(cp)) {
                return new BlockPos(cp.getStartX(), flightLevel.get(), cp.getStartZ());
            }
        }
        return null;
    }
    // Check if pattern is complete (all chunks loaded)
    private boolean isPatternComplete() {
        // For demo: if more than 256 chunks loaded, consider complete
        return loadedChunks.size() > 256;
    }

    private BlockPos getNextDestination(BlockPos start) {
        var multi = multipliers[multiIndex];
        multiIndex = (multiIndex + 1) % multipliers.length;

        var out = new BlockPos(
                start.getX() + (multi[0] * sideDistance),
                flightLevel.get(),
                start.getZ() + (multi[1] * sideDistance)
        );

        sideDistance += gapDistance.get() * 16;

        return out;
    }

    @Override
    public void onDeactivate() {
        if (efly == null || !efly.isActive()) return;
        BaritoneAPI.getProvider().getPrimaryBaritone().getCommandManager().execute("stop");
    }


    @Override
    public void onActivate() {
        this.reset();
        dimension = mc.world.getDimensionEntry();
        lastDest = getNextDestination(mc.player.getBlockPos());
        efly.pathTo(lastDest);
    }
}