package dev.journey.Skylandia.modules.render;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.entity.EntityUtils;
import meteordevelopment.meteorclient.utils.entity.DamageUtils;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.RaycastContext;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class CrystalPositionESP extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgFiltering = settings.createGroup("Filtering");
    private final SettingGroup sgRender = settings.createGroup("Render");

    // General Settings
    private final Setting<Integer> maxPositions = sgGeneral.add(new IntSetting.Builder()
        .name("max-positions")
        .description("Maximum number of crystal positions to show.")
        .defaultValue(50)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<Double> maxDistance = sgGeneral.add(new DoubleSetting.Builder()
        .name("max-distance")
        .description("Maximum distance to search for crystal positions.")
        .defaultValue(12.0)
        .min(1.0)
        .max(20.0)
        .build()
    );

    private final Setting<Boolean> onlyWhenHoldingCrystal = sgGeneral.add(new BoolSetting.Builder()
        .name("only-when-holding-crystal")
        .description("Only show positions when holding an end crystal.")
        .defaultValue(false)
        .build()
    );

    // Filtering Settings
    private final Setting<Double> minDamageToTarget = sgFiltering.add(new DoubleSetting.Builder()
        .name("min-damage-to-target")
        .description("Minimum damage to target to show position.")
        .defaultValue(2.0)
        .min(0.0)
        .max(36.0)
        .build()
    );

    private final Setting<Double> maxSelfDamage = sgFiltering.add(new DoubleSetting.Builder()
        .name("max-self-damage")
        .description("Maximum self damage allowed.")
        .defaultValue(8.0)
        .min(0.0)
        .max(36.0)
        .build()
    );

    private final Setting<Double> minRatio = sgFiltering.add(new DoubleSetting.Builder()
        .name("min-ratio")
        .description("Minimum damage ratio (target damage / self damage).")
        .defaultValue(1.5)
        .min(0.0)
        .max(10.0)
        .build()
    );

    private final Setting<Boolean> antiSuicide = sgFiltering.add(new BoolSetting.Builder()
        .name("anti-suicide")
        .description("Don't show positions that would kill you.")
        .defaultValue(true)
        .build()
    );

    // Render Settings
    private final Setting<Boolean> showBestOnly = sgRender.add(new BoolSetting.Builder()
        .name("show-best-only")
        .description("Only show the best crystal position.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> showDamageText = sgRender.add(new BoolSetting.Builder()
        .name("show-damage-text")
        .description("Show damage numbers above positions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<ShapeMode> shapeMode = sgRender.add(new EnumSetting.Builder<ShapeMode>()
        .name("shape-mode")
        .description("How the shapes are rendered.")
        .defaultValue(ShapeMode.Both)
        .build()
    );

    private final Setting<SettingColor> bestPositionColor = sgRender.add(new ColorSetting.Builder()
        .name("best-position-color")
        .description("Color for the best crystal position.")
        .defaultValue(new SettingColor(255, 0, 0, 150))
        .build()
    );

    private final Setting<SettingColor> goodPositionColor = sgRender.add(new ColorSetting.Builder()
        .name("good-position-color")
        .description("Color for good crystal positions.")
        .defaultValue(new SettingColor(255, 255, 0, 100))
        .build()
    );

    private final Setting<SettingColor> okPositionColor = sgRender.add(new ColorSetting.Builder()
        .name("ok-position-color")
        .description("Color for okay crystal positions.")
        .defaultValue(new SettingColor(0, 255, 0, 80))
        .build()
    );

    private final Setting<Boolean> showTargetLines = sgRender.add(new BoolSetting.Builder()
        .name("show-target-lines")
        .description("Draw lines from crystal positions to their targets.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> showPlayerLines = sgRender.add(new BoolSetting.Builder()
        .name("show-player-lines")
        .description("Draw lines from crystal positions to yourself.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> lineWidth = sgRender.add(new DoubleSetting.Builder()
        .name("line-width")
        .description("Width of the targeting lines.")
        .defaultValue(2.0)
        .min(0.5)
        .max(5.0)
        .visible(() -> showTargetLines.get() || showPlayerLines.get())
        .build()
    );

    private final Setting<Boolean> showRankings = sgRender.add(new BoolSetting.Builder()
        .name("show-rankings")
        .description("Show position rankings (1st, 2nd, 3rd, etc).")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> fadeDistance = sgRender.add(new IntSetting.Builder()
        .name("fade-distance")
        .description("Distance at which crystal positions start to fade.")
        .defaultValue(8)
        .min(1)
        .max(20)
        .build()
    );

    private final Setting<SettingColor> targetLineColor = sgRender.add(new ColorSetting.Builder()
        .name("target-line-color")
        .description("Color for lines to targets.")
        .defaultValue(new SettingColor(255, 0, 0, 150))
        .visible(() -> showTargetLines.get())
        .build()
    );

    private final Setting<SettingColor> playerLineColor = sgRender.add(new ColorSetting.Builder()
        .name("player-line-color")
        .description("Color for lines to yourself.")
        .defaultValue(new SettingColor(0, 255, 255, 100))
        .visible(() -> showPlayerLines.get())
        .build()
    );

    private final Setting<SettingColor> textColor = sgRender.add(new ColorSetting.Builder()
        .name("text-color")
        .description("Color for damage text.")
        .defaultValue(new SettingColor(255, 255, 255, 255))
        .build()
    );

    // Data structures
    private final Map<BlockPos, CrystalPosition> validPositions = new ConcurrentHashMap<>();
    private CrystalPosition bestPosition = null;
    private int updateTimer = 0;

    public CrystalPositionESP() {
        super(Skylandia.Render, "crystal-position-esp", "Shows valid crystal positions with damage calculations.");
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        updateTimer++;
        if (updateTimer >= 5) { // Update every 5 ticks (4 times per second)
            updateTimer = 0;
            updateCrystalPositions();
        }
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        if (onlyWhenHoldingCrystal.get() && !isHoldingCrystal()) {
            return;
        }

        if (showBestOnly.get()) {
            if (bestPosition != null) {
                renderCrystalPosition(event, bestPosition, true);
            }
        } else {
            for (CrystalPosition pos : validPositions.values()) {
                renderCrystalPosition(event, pos, pos == bestPosition);
            }
        }
    }

    private void updateCrystalPositions() {
        validPositions.clear();
        bestPosition = null;

        if (mc.player == null || mc.world == null) return;

        // Find target players
        List<PlayerEntity> targets = new ArrayList<>();
        for (Entity entity : mc.world.getEntities()) {
            if (entity instanceof PlayerEntity player && 
                player != mc.player && 
                !player.isDead() && 
                mc.player.distanceTo(player) <= maxDistance.get() * 2) {
                targets.add(player);
            }
        }

        if (targets.isEmpty()) return;

        // Search for valid crystal positions
        BlockPos playerPos = mc.player.getBlockPos();
        int range = (int) Math.ceil(maxDistance.get());

        for (int x = -range; x <= range; x++) {
            for (int y = -range; y <= range; y++) {
                for (int z = -range; z <= range; z++) {
                    BlockPos pos = playerPos.add(x, y, z);
                    
                    if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > maxDistance.get()) continue;
                    
                    if (isValidCrystalPosition(pos)) {
                        CrystalPosition crystalPos = calculateDamages(pos, targets);
                        if (crystalPos != null && isPositionViable(crystalPos)) {
                            validPositions.put(pos, crystalPos);
                            
                            if (bestPosition == null || crystalPos.score > bestPosition.score) {
                                bestPosition = crystalPos;
                            }
                        }
                    }
                }
            }
        }

        // Limit positions shown and assign rankings
        List<CrystalPosition> sortedPositions = new ArrayList<>(validPositions.values());
        sortedPositions.sort((a, b) -> Double.compare(b.score, a.score));
        
        if (sortedPositions.size() > maxPositions.get()) {
            validPositions.clear();
            for (int i = 0; i < maxPositions.get() && i < sortedPositions.size(); i++) {
                CrystalPosition pos = sortedPositions.get(i);
                pos.rank = i + 1; // Assign ranking
                validPositions.put(pos.pos, pos);
            }
        } else {
            // Assign rankings to all positions
            for (int i = 0; i < sortedPositions.size(); i++) {
                sortedPositions.get(i).rank = i + 1;
            }
        }
    }

    private boolean isValidCrystalPosition(BlockPos pos) {
        if (!mc.world.getBlockState(pos).isAir()) return false;
        if (!mc.world.getBlockState(pos.up()).isAir()) return false;
        
        Block below = mc.world.getBlockState(pos.down()).getBlock();
        if (below != Blocks.OBSIDIAN && below != Blocks.BEDROCK) return false;
        
        // Check for existing crystals
        for (Entity entity : mc.world.getEntities()) {
            if (entity instanceof EndCrystalEntity && entity.getBlockPos().equals(pos)) {
                return false;
            }
        }
        
        return true;
    }

    private CrystalPosition calculateDamages(BlockPos pos, List<PlayerEntity> targets) {
        Vec3d crystalPos = Vec3d.ofCenter(pos);
        
        double selfDamage = DamageUtils.crystalDamage(mc.player, crystalPos);
        double maxTargetDamage = 0;
        PlayerEntity bestTarget = null;
        
        for (PlayerEntity target : targets) {
            double damage = DamageUtils.crystalDamage(target, crystalPos);
            if (damage > maxTargetDamage) {
                maxTargetDamage = damage;
                bestTarget = target;
            }
        }
        
        if (maxTargetDamage < minDamageToTarget.get()) return null;
        
        double ratio = selfDamage > 0 ? maxTargetDamage / selfDamage : maxTargetDamage;
        double score = maxTargetDamage - selfDamage * 0.5; // Prefer high target damage, low self damage
        
        return new CrystalPosition(pos, selfDamage, maxTargetDamage, ratio, score, bestTarget);
    }

    private boolean isPositionViable(CrystalPosition pos) {
        if (pos.selfDamage > maxSelfDamage.get()) return false;
        if (pos.ratio < minRatio.get()) return false;
        if (antiSuicide.get() && pos.selfDamage >= mc.player.getHealth() + mc.player.getAbsorptionAmount()) return false;
        
        return true;
    }

    private void renderCrystalPosition(Render3DEvent event, CrystalPosition pos, boolean isBest) {
        Vec3d crystalCenter = Vec3d.ofCenter(pos.pos);
        double distance = mc.player.getPos().distanceTo(crystalCenter);
        
        // Calculate fade alpha based on distance
        float fadeAlpha = 1.0f;
        if (distance > fadeDistance.get()) {
            fadeAlpha = Math.max(0.1f, 1.0f - (float)(distance - fadeDistance.get()) / fadeDistance.get());
        }
        
        Color color = getPositionColor(pos, isBest);
        color = new Color(color.r, color.g, color.b, (int)(color.a * fadeAlpha));
        
        // Render crystal position box
        event.renderer.box(pos.pos, color, color, shapeMode.get(), 0);
        
        // Render targeting lines
        if (showTargetLines.get() && pos.target != null) {
            Vec3d targetPos = pos.target.getPos().add(0, pos.target.getHeight() / 2, 0);
            Color lineColor = new Color(targetLineColor.get().r, targetLineColor.get().g, 
                                      targetLineColor.get().b, (int)(targetLineColor.get().a * fadeAlpha));
            event.renderer.line(
                crystalCenter.x, crystalCenter.y, crystalCenter.z,
                targetPos.x, targetPos.y, targetPos.z,
                lineColor
            );
        }
        
        if (showPlayerLines.get() && mc.player != null) {
            Vec3d playerPos = mc.player.getPos().add(0, mc.player.getHeight() / 2, 0);
            Color lineColor = new Color(playerLineColor.get().r, playerLineColor.get().g, 
                                      playerLineColor.get().b, (int)(playerLineColor.get().a * fadeAlpha));
            event.renderer.line(
                crystalCenter.x, crystalCenter.y, crystalCenter.z,
                playerPos.x, playerPos.y, playerPos.z,
                lineColor
            );
        }
        
        // Render damage text
        if (showDamageText.get()) {
            Vec3d textPos = crystalCenter.add(0, 1.2, 0);
            String text = String.format("%.1f/%.1f", pos.targetDamage, pos.selfDamage);
            
            // Add ranking if enabled
            if (showRankings.get() && pos.rank > 0) {
                text = "#" + pos.rank + " " + text;
            }
            
            TextRenderer textRenderer = TextRenderer.get();
            double textWidth = textRenderer.getWidth(text, false);
            
            Color fadedTextColor = new Color(textColor.get().r, textColor.get().g, 
                                           textColor.get().b, (int)(textColor.get().a * fadeAlpha));
            
            textRenderer.render(
                text,
                textPos.x - textWidth / 2.0,
                textPos.y,
                fadedTextColor,
                false
            );
            
            // Add target name if available
            if (pos.target != null && isBest) {
                String targetName = pos.target.getName().getString();
                Vec3d namePos = textPos.add(0, -0.3, 0);
                double nameWidth = textRenderer.getWidth(targetName, false);
                
                Color fadedTargetColor = new Color(targetLineColor.get().r, targetLineColor.get().g, 
                                                 targetLineColor.get().b, (int)(targetLineColor.get().a * fadeAlpha));
                
                textRenderer.render(
                    targetName,
                    namePos.x - nameWidth / 2.0,
                    namePos.y,
                    fadedTargetColor,
                    false
                );
            }
        }
    }

    private Color getPositionColor(CrystalPosition pos, boolean isBest) {
        if (isBest) return bestPositionColor.get();
        
        if (pos.targetDamage >= 6.0) return goodPositionColor.get();
        return okPositionColor.get();
    }

    private boolean isHoldingCrystal() {
        return InvUtils.testInHands(Items.END_CRYSTAL);
    }

    private static class CrystalPosition {
        final BlockPos pos;
        final double selfDamage;
        final double targetDamage;
        final double ratio;
        final double score;
        final PlayerEntity target;
        int rank = 0; // Position ranking (1 = best, 2 = second best, etc)
        
        CrystalPosition(BlockPos pos, double selfDamage, double targetDamage, double ratio, double score, PlayerEntity target) {
            this.pos = pos;
            this.selfDamage = selfDamage;
            this.targetDamage = targetDamage;
            this.ratio = ratio;
            this.score = score;
            this.target = target;
        }
    }
}
