package dev.journey.Skylandia.events;

import net.minecraft.entity.Entity;

public class DamageEvent {
    private final Entity entity;
    private final float amount;
    private final boolean isCritical;
    private final boolean isPlayer;
    private final long timestamp;

    public DamageEvent(Entity entity, float amount, boolean isCritical, boolean isPlayer) {
        this.entity = entity;
        this.amount = amount;
        this.isCritical = isCritical;
        this.isPlayer = isPlayer;
        this.timestamp = System.currentTimeMillis();
    }

    public Entity getEntity() {
        return entity;
    }

    public float getAmount() {
        return amount;
    }

    public boolean isCritical() {
        return isCritical;
    }

    public boolean isPlayer() {
        return isPlayer;
    }

    public long getTimestamp() {
        return timestamp;
    }
}