# DemonCrystal Module - Comprehensive Documentation

## Overview

DemonCrystal is an advanced, highly-configurable Minecraft combat automation module focused on "crystal PvP" (End Crystal combat). It automates crystal placement, breaking, targeting, escape, and defensive maneuvers with extensive safety and performance controls. The module is designed for use within the Meteor Client mod framework and integrates deeply with other automation and utility systems in the Skylandia project.

---

## Core Architecture

**Base Module:** `Module` (Meteor Client framework)  
**Event System:** Uses `@EventHandler` for tick, render, and packet events  
**File Location:** `dev.journey.Skylandia.modules.automation.DemonCrystal`  
**Total Lines:** 7,909 lines of code  
**Setting Groups:** 15 organized feature categories

---

## Major Feature Categories

### 🎯 **Target Acquisition System**
- **AI-Enhanced Targeting:** Smart target selection with predictive algorithms
- **Target Priority Modes:** Closest, Lowest Health, Highest Damage, Most/Least Armor
- **Movement Prediction:** Advanced prediction system with configurable lookahead (1-60 ticks)
- **Entity Filter:** Configurable entity types (<PERSON>, Warden, Wither, etc.)
- **Smart Switching:** Dynamic target switching based on damage potential
- **Predictive Accuracy:** Adjustable prediction precision (0.1-1.0)

### 💎 **Crystal Placement System**
- **Enhanced Placement:** Existence verification and validation
- **Range Control:** Separate ranges for normal (5.0) and wall placement (3.5)
- **Legacy Support:** 1.12 placement compatibility mode
- **Support Blocks:** Automatic support block placement (Accurate/Fast/Smart/Disabled)
- **Face-Place Mode:** Aggressive close-range targeting for low-health enemies
- **Valid Base Blocks:** Configurable crystal base blocks (Obsidian, Bedrock, etc.)
- **Placement Verification:** Configurable delay and timeout for placement validation

### 💥 **Crystal Breaking System**
- **Enhanced Breaking:** Existence verification and anti-ghost protection
- **Inhibit Mode:** Defensive crystal breaking with placement tracking
- **Burst Breaking:** Rapid-fire destruction (configurable count and delay)
- **Packet Breaking:** Low-level packet manipulation for maximum speed
- **Instant Break:** Immediate crystal destruction upon detection
- **Break Attempts:** Configurable retry attempts per crystal (1-10)
- **Damage Threshold:** Minimum damage requirements for breaking (0-36)

### ⚡ **Multi-Crystal Warfare**
- **Deployment Modes:** Dual, Triple, Quad, Penta, Adaptive, Burst
- **Placement Patterns:** Surrounding, Linear, Cross, Diamond, Optimal Damage, Trap Formation
- **Synchronized Detonation:** Coordinated multi-crystal explosions
- **Intelligent Spacing:** Optimal distance calculation between crystals
- **Overlap Avoidance:** Prevent damage overlap for maximum efficiency
- **Adaptive Count:** Dynamic crystal count based on target health/distance

### 🛡️ **Safety & Protection Systems**
- **Anti-Suicide:** Prevents self-damaging crystal operations
- **Emergency Disable:** Automatic shutdown in dangerous situations
- **Totem Safety:** Enhanced protection when holding totems
- **Armor Safety:** Durability-based aggression adjustment
- **Hole Safety:** Safe hole detection and validation
- **Surround Protection:** Automatic self-protection block placement
- **Self-Damage Limits:** Configurable maximum self-damage (0-36)
- **Safety Checks:** Multi-layered validation before actions

### 🌀 **Emergency Escape System**
- **Escape Methods:** Chorus Fruit, Ender Pearl, or Both
- **Auto-Pearl Modes:** Health-based, Totem-based, Combined, Smart
- **Health Thresholds:** Configurable trigger points (1-20 health)
- **Totem Thresholds:** Minimum totem count for escape (0-5)
- **Distance Control:** Minimum escape distance (5-30 blocks)
- **Cooldown Management:** Anti-spam protection (20-100 ticks)
- **Target-Based Escape:** Only escape when actively targeted

### 🕳️ **Target Trapping System**
- **Trap Placement:** Above and side block placement
- **Smart Trapping:** AI-based trap positioning
- **Range Control:** Configurable trap range (1-6 blocks)
- **Block Limits:** Maximum trap blocks per target (1-12)
- **Health-Based Trapping:** Low-health target prioritization
- **Escape Route Blocking:** Strategic movement denial

### ⛏️ **City Mining Integration**
- **Automatic Mining:** Obsidian block removal near enemy feet
- **Support Blocks:** Pre-placement of support blocks before mining
- **Packet Mining:** Reliable server-validated block breaking
- **Instant Tool Switching:** Automatic pickaxe selection
- **Visual Feedback:** Real-time mining progress display
- **Validation:** Block break confirmation and retry logic
- **Range Control:** Configurable mining range (1-12 blocks)
- **Tool Selection:** Configurable pickaxe types

### 🎨 **Advanced Chams Rendering**
- **Entity Customization:** Players, End Crystals, Hand rendering
- **Shader Effects:** Image shader with color customization
- **Model Scaling:** Adjustable entity scales and effects
- **Texture Control:** Enable/disable textures per entity type
- **Crystal Effects:** Bounce, rotation speed, and color customization
- **Through-Wall Rendering:** See entities through walls
- **Self-Exclusion:** Option to ignore self in rendering

### 👟 **Movement Enhancement Suite**

#### Step Enhancement
- **Modes:** NCP and Vanilla step modes
- **Height Control:** Adjustable step height (1.0-2.5 blocks)
- **Timer Integration:** Timer manipulation for smoother stepping
- **Strict Mode:** Enhanced anti-cheat compatibility
- **Hole Integration:** Auto-disable when entering holes
- **Fast Fall:** Accelerated falling over holes

#### Hole Navigation
- **Auto-Anchor:** Automatic hole anchoring when looking down
- **Hole Snap:** Automatic navigation to nearest safe holes
- **Search Parameters:** Range (1-20) and FOV (1-360°) control
- **Safety Checks:** Enemy distance validation
- **Timer Usage:** Configurable speed manipulation
- **Auto-Disable:** Smart deactivation in various scenarios

#### Advanced Strafe
- **Boost Modes:** None, Elytra Boost, Damage Boost
- **Speed Control:** Configurable strafe multipliers
- **Anti-Knockback:** Knockback reduction during movement
- **FOV Management:** Disable FOV effects for stable aiming
- **Sunrise Compatibility:** Enhanced elytra mechanics

#### Hole Filling
- **Fill Modes:** Always fill or target-based filling
- **Hole Types:** Single, Double, Quad hole support
- **Block Selection:** All blocks, Obsidian, Webs, Indestructible
- **Self-Fill:** Burrow and trap mode self-positioning
- **Range Control:** Separate ranges for normal and wall placement

### 🔧 **Armor Mending System**
- **Per-Piece Thresholds:** Individual durability settings for each armor piece
- **Max Durability Targets:** Configurable repair completion points
- **XP Bottle Management:** Automatic experience bottle usage
- **Rotation Control:** LookUp, LookDown, LookAtFeet modes
- **Server-Side Rotation:** Packet-based rotation for consistent targeting
- **Mending Delays:** Configurable timing between XP bottle throws
- **Pause Integration:** Option to pause all actions during mending

### 🕳️ **Air Block Filling System**
- **Smart Detection:** Automatic air block identification
- **Hole Prioritization:** Bedrock hole preference over obsidian
- **Range/Height Control:** Configurable scan area (1-5 radius, 1-4 height)
- **Safety Integration:** Only fill when in safe holes
- **Block Rate Limiting:** Configurable blocks per tick (1-8)
- **Visual Rendering:** Air block highlighting and preview

### ⏱️ **Timing & Performance Controls**
- **Delay Management:** Separate delays for place/break/switch actions
- **Speed Modes:** Maximum speed optimizations
- **No-Delay Mode:** Remove all delays for peak performance
- **Tick Optimization:** Multiple actions per tick (1-10)
- **Packet Optimization:** Timing control for server compatibility
- **BPS Throttling:** Blocks-per-second limiting (0.5-20.0)

### 🔄 **Item Management**
- **Switch Modes:** Disabled, Normal, Silent, Instant
- **Anti-Weakness:** Automatic tool switching for weakness effect
- **Gap Protection:** Prevent switching while holding gapples
- **Crystal Detection:** Automatic crystal finding and switching
- **Tool Selection:** Smart pickaxe and item selection

### 🍎 **AutoGap Integration**
- **Health Monitoring:** Automatic golden apple consumption
- **Threshold Control:** Configurable health trigger (1-40 hearts)
- **Action Pausing:** Pause combat during healing
- **Inventory Management:** Smart gapple detection and switching

### 👣 **Foot Targeting System**
- **Precision Targeting:** Accurate foot-level crystal placement
- **Obsidian Mining:** Automatic obstacle removal
- **Range Control:** Separate foot targeting range (1-6 blocks)
- **Mining Limits:** Maximum obsidian blocks per target (1-5)
- **Progress Tracking:** Mining progress monitoring and display

---

## Comprehensive Settings Groups

### 🎯 Target Acquisition (12 settings)
- Target range, priority, prediction, entity filtering
- Smart targeting, target switching, prediction accuracy
- Movement prediction with configurable lookahead

### 💎 Crystal Placement (15 settings)
- Placement range, wall range, minimum damage
- Face-place mode, support blocks, valid base blocks
- Legacy 1.12 compatibility, verification delays

### 💥 Crystal Breaking (12 settings)
- Break range, minimum damage, inhibit mode
- Burst breaking, packet breaking, instant break
- Break attempts, verification, anti-ghost

### ⚡ Multi-Crystal Warfare (9 settings)
- Multi-crystal modes, placement patterns
- Maximum crystals, range, delay, synchronization
- Minimum damage, intelligent spacing

### 🛡️ Safety Systems (15 settings)
- Anti-suicide, emergency disable, totem safety
- Armor safety, hole safety, surround protection
- Self-damage limits, safety checks

### 🌀 Emergency Escape (8 settings)
- Escape methods, auto-pearl modes
- Health/totem thresholds, distance control
- Cooldown management, target-based escape

### 🕳️ Target Trapping (8 settings)
- Trap above/sides, range, block limits
- Smart trapping, health-based activation

### ⛏️ City Mining (12 settings)
- Support blocks, visual feedback, packet mining
- Range, tool selection, instant switching
- Validation, delays, rendering options

### 🎨 Chams Rendering (15 settings)
- Entity selection, shader effects, colors
- Model scaling, texture control, effects
- Through-wall rendering, self-exclusion

### 👟 Movement Enhancement (25+ settings)
- Step: modes, height, timer, strict mode
- Hole Anchor: pitch, pull, strength
- Hole Snap: mode, range, FOV, timer
- Strafe: boost modes, speed, anti-knockback
- Hole Fill: modes, types, ranges, delays

### 🔧 Armor Mending (16 settings)
- Per-piece durability thresholds and targets
- XP bottle management, rotation control
- Server-side rotation, delays, pause integration

### 🕳️ Air Block Filling (10 settings)
- Range/height control, hole prioritization
- Safety integration, rate limiting, rendering

### ⏱️ Timing & Performance (8 settings)
- Delay controls, speed modes, optimizations
- Tick optimization, packet timing, BPS throttling

### 🔄 Item Management (4 settings)
- Switch modes, anti-weakness, gap protection

### 🍎 AutoGap Integration (3 settings)
- Health monitoring, threshold control

---

## Advanced Features

### 🧠 **AI-Enhanced Systems**
- **Predictive Targeting:** Machine learning-based movement prediction
- **Smart Placement:** Optimal position calculation algorithms
- **Adaptive Behavior:** Dynamic adjustment based on combat conditions
- **Threat Assessment:** Real-time danger evaluation

### 📡 **Packet-Level Operations**
- **Low-Level Breaking:** Direct packet manipulation for crystal destruction
- **Placement Validation:** Server-side confirmation systems
- **Anti-Cheat Bypass:** Sophisticated timing and behavior mimicking
- **Network Optimization:** Minimal latency packet sending

### 🎮 **Keybind System**
- **Hyper-Aggressive Toggle:** Instant aggression mode switching
- **Face-Place Toggle:** Quick low-health targeting activation
- **Emergency Escape:** Manual escape trigger
- **Inhibit Mode Toggle:** Defensive mode switching
- **Strafe Controls:** Movement mode cycling
- **Hole Fill Toggle:** Area control activation

### 🔍 **Debug & Visualization**
- **Debug Mode:** Comprehensive information display
- **Visual Rendering:** Real-time combat visualization
- **Damage Numbers:** Floating damage display
- **Target Highlighting:** Current target visualization
- **Prediction Paths:** Movement prediction rendering
- **Block Highlighting:** Placement/break position display

### 🛡️ **Defensive Systems**
- **Inhibit Placement:** Enemy crystal spot blocking
- **Defensive Pillars:** Automatic counter-structure building
- **Surround Protection:** Multi-radius self-protection
- **Escape Integration:** Seamless emergency systems

### ⚡ **Performance Optimizations**
- **Batch Processing:** Multiple actions per tick
- **Memory Management:** Efficient data structure usage
- **CPU Optimization:** Minimal computational overhead
- **Anti-Lag Systems:** Frame rate protection

---

## Integration Points

### **Meteor Client Framework**
- Settings system integration with 15 organized groups
- Event system utilization for real-time response
- Rendering system integration for visual feedback
- Module lifecycle management

### **Skylandia Ecosystem**
- Notification system integration (Toast/Chat)
- Friends system integration for target filtering
- Utility class dependencies for core functions
- Cross-module state sharing

### **Minecraft Systems**
- Direct packet manipulation for speed/bypass
- World state monitoring and modification
- Player state management and control
- Entity tracking and prediction

---

## Technical Implementation

### **Code Organization**
- **Enums:** 15+ enums for feature configuration
- **State Variables:** 50+ variables for system state tracking
- **Collections:** Maps, Lists, Sets for data management
- **Event Handlers:** Pre/Post tick, Render3D, Packet events

### **Safety Architecture**
- **Multi-Layer Validation:** Comprehensive safety checking
- **Fallback Systems:** Graceful degradation on errors
- **Emergency Protocols:** Automatic shutdown mechanisms
- **Anti-Suicide Logic:** Self-preservation prioritization

### **Performance Design**
- **Efficient Algorithms:** Optimized pathfinding and calculation
- **Memory Pooling:** Reduced garbage collection impact
- **Lazy Evaluation:** Compute only when necessary
- **Batch Operations:** Multiple actions per cycle

---

## Configuration Complexity

### **Total Settings:** 200+ individual settings
### **Feature Toggles:** 50+ boolean switches
### **Numeric Controls:** 100+ sliders and inputs
### **Enum Selections:** 50+ dropdown choices
### **Keybinds:** 10+ hotkey assignments
### **Color Settings:** 20+ visual customizations

---

## Combat Effectiveness

### **Offensive Capabilities**
- Multi-crystal simultaneous placement (up to 5 crystals)
- Predictive targeting with movement compensation
- Face-place mode for low-health elimination
- Burst breaking for overwhelming firepower

### **Defensive Capabilities**
- Instant inhibit breaking of enemy crystals
- Automatic surround and hole protection
- Emergency escape with multiple methods
- Adaptive safety based on health/armor/totems

### **Tactical Features**
- Target trapping and movement denial
- City mining for position breaking
- Hole filling for area control
- Defensive pillar construction

---

## Summary

DemonCrystal represents the pinnacle of Minecraft crystal PvP automation, combining advanced AI algorithms, comprehensive safety systems, and extensive customization options into a single, cohesive combat module. With over 200 configurable settings across 15 major feature categories, it provides unparalleled control over every aspect of crystal combat while maintaining robust safety mechanisms and performance optimizations.

The module's architecture emphasizes modularity, safety, and adaptability, making it suitable for various combat scenarios from defensive play to aggressive multi-crystal warfare. Its integration with the broader Skylandia ecosystem and extensive debugging capabilities make it both powerful and maintainable for advanced users.