package dev.journey.Skylandia.modules.exploration;


import dev.journey.Skylandia.Skylandia;
import dev.journey.Skylandia.mixin.accessor.ChunkDataEventAccessor;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.ChunkDataEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.network.packet.s2c.play.BlockUpdateS2CPacket;
import net.minecraft.network.packet.s2c.play.ChunkDeltaUpdateS2CPacket;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.chunk.WorldChunk;
import net.minecraft.world.chunk.ChunkSection;
import xaeroplus.module.ModuleManager;
import xaeroplus.module.impl.PaletteNewChunks;
import xaeroplus.module.impl.OldChunks;
import static dev.journey.Skylandia.utils.SkylandiaUtil.sendWebhook;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.EnumMap;
import java.util.Map;

public class Trails extends Module {
    // Removed duplicate getOldChunks() method to resolve compilation error
    // --- Detection Mode ---
    public enum DetectMode {
        Normal,
        IgnoreBlockExploit,
        BlockExploitMode
    }

    // --- Chunk Types ---
    public enum ChunkType {
        NEW,
        OLD,
        BEING_UPDATED,
        OLD_GENERATION,
        TICK_EXPLOIT,
        UNKNOWN
    }

    // --- Settings Groups ---
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgDetection = settings.createGroup("Advanced Detection");
    private final SettingGroup sgSensitivity = settings.createGroup("Detection Sensitivity");
    private final SettingGroup sgCdata = settings.createGroup("Saved Chunk Data");
    private final SettingGroup sgCache = settings.createGroup("Cached Chunk Data");
    private final SettingGroup sgRender = settings.createGroup("Rendering");
    private final SettingGroup sgPerformance = settings.createGroup("Performance");
    private final SettingGroup sgNotifications = settings.createGroup("Notifications");
    private final SettingGroup sgPerType = settings.createGroup("Per-Chunk-Type Settings");

    // --- Detection Settings (Advanced & User-Friendly) ---
    private final Setting<Boolean> paletteExploit = sgDetection.add(new BoolSetting.Builder()
            .name("Palette Exploit Detection")
            .description("Detects new chunks by analyzing the order of chunk section palettes. Recommended for 1.18+ servers. Highlights chunks being updated from an old version.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> beingUpdatedDetector = sgDetection.add(new BoolSetting.Builder()
            .name("Legacy Chunk Update Detection")
            .description("Marks chunks as a unique color if they are currently being updated from an old version (<=1.17). Useful for migration tracking.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> overworldOldChunksDetector = sgDetection.add(new BoolSetting.Builder()
            .name("Overworld Old Chunk Detector (Pre-1.17)")
            .description("Detects chunks generated in old versions by checking for specific blocks above Y=0 in the Overworld.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> netherOldChunksDetector = sgDetection.add(new BoolSetting.Builder()
            .name("Nether Old Chunk Detector (Pre-1.16)")
            .description("Detects old Nether chunks by checking for missing blocks introduced in newer Nether updates.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> endOldChunksDetector = sgDetection.add(new BoolSetting.Builder()
            .name("End Old Chunk Detector (Pre-1.13)")
            .description("Detects old End chunks by checking for the biome 'minecraft:the_end'.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> realTimeDetection = sgDetection.add(new BoolSetting.Builder()
            .name("Real-Time Detection")
            .description("Enables real-time detection using chunk data packets for immediate feedback. May impact performance on slower systems.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> liquidExploit = sgDetection.add(new BoolSetting.Builder()
            .name("Liquid Exploit Detection")
            .description("Estimates new chunks based on flowing liquids. Useful for finding recently generated terrain.")
            .defaultValue(false)
            .build()
    );
    private final Setting<Boolean> blockUpdateExploit = sgDetection.add(new BoolSetting.Builder()
            .name("Block Update Exploit Detection")
            .description("Estimates new chunks based on block updates. May produce false positives; use Block Exploit Mode for accuracy.")
            .defaultValue(false)
            .build()
    );
    private final Setting<DetectMode> detectMode = sgDetection.add(new EnumSetting.Builder<DetectMode>()
            .name("Detection Mode")
            .description("Choose detection logic: Normal, Ignore Block Exploit, or Block Exploit Mode for legacy servers.")
            .defaultValue(DetectMode.Normal)
            .build()
    );

    // --- Per-chunk-type settings ---
    private final Map<ChunkType, Setting<Boolean>> renderToggles = new EnumMap<>(ChunkType.class);
    private final Map<ChunkType, Setting<SettingColor>> sideColors = new EnumMap<>(ChunkType.class);
    private final Map<ChunkType, Setting<SettingColor>> lineColors = new EnumMap<>(ChunkType.class);
    private final Map<ChunkType, Setting<ShapeMode>> shapeModes = new EnumMap<>(ChunkType.class);
    private final Map<ChunkType, Setting<Boolean>> notificationToggles = new EnumMap<>(ChunkType.class);

    {
        // Settings for each chunk type
        for (ChunkType type : ChunkType.values()) {
            String typeName = type.name().toLowerCase().replace('_', '-');
            renderToggles.put(type, sgPerType.add(new BoolSetting.Builder()
                .name(typeName + "-render-enabled")
                .description("Enable rendering for " + typeName + " chunks.")
                .defaultValue(true)
                .build()
            ));
            sideColors.put(type, sgPerType.add(new ColorSetting.Builder()
                .name(typeName + "-side-color")
                .description("Side color for " + typeName + " chunks.")
                .defaultValue(getDefaultSideColor(type))
                .build()
            ));
            lineColors.put(type, sgPerType.add(new ColorSetting.Builder()
                .name(typeName + "-line-color")
                .description("Line color for " + typeName + " chunks.")
                .defaultValue(getDefaultLineColor(type))
                .build()
            ));
            shapeModes.put(type, sgPerType.add(new EnumSetting.Builder<ShapeMode>()
                .name(typeName + "-shape-mode")
                .description("Shape mode for " + typeName + " chunks.")
                .defaultValue(ShapeMode.Both)
                .build()
            ));
            notificationToggles.put(type, sgNotifications.add(new BoolSetting.Builder()
                .name(typeName + "-notify")
                .description("Enable notifications for " + typeName + " chunks.")
                .defaultValue(true)
                .build()
            ));
        }
    }

    // --- Default color helpers ---
    private SettingColor getDefaultSideColor(ChunkType type) {
        switch (type) {
            case NEW: return new SettingColor(255, 50, 50, 120);        // Bright red - most important chunks
            case OLD: return new SettingColor(50, 255, 50, 80);         // Bright green - old chunks
            case BEING_UPDATED: return new SettingColor(255, 165, 0, 100); // Orange - chunks being updated
            case OLD_GENERATION: return new SettingColor(255, 255, 0, 90);  // Yellow - old generation chunks
            case TICK_EXPLOIT: return new SettingColor(138, 43, 226, 110);  // Blue violet - exploit chunks
            case UNKNOWN: return new SettingColor(169, 169, 169, 70);   // Light gray - unknown chunks
            default: return new SettingColor(169, 169, 169, 70);
        }
    }
    private SettingColor getDefaultLineColor(ChunkType type) {
        switch (type) {
            case NEW: return new SettingColor(255, 0, 0, 255);          // Pure red - maximum visibility
            case OLD: return new SettingColor(0, 255, 0, 180);          // Bright green - good visibility
            case BEING_UPDATED: return new SettingColor(255, 140, 0, 220); // Dark orange - strong contrast
            case OLD_GENERATION: return new SettingColor(255, 215, 0, 200); // Gold - distinct from orange
            case TICK_EXPLOIT: return new SettingColor(75, 0, 130, 255); // Indigo - unique color
            case UNKNOWN: return new SettingColor(105, 105, 105, 150);   // Dim gray - subtle but visible
            default: return new SettingColor(105, 105, 105, 150);
        }
    }

    // --- Performance Settings ---
    private final Setting<Integer> maxChunksPerFrame = sgPerformance.add(new IntSetting.Builder()
        .name("Max Chunks Per Frame")
        .description("Limits the number of chunks rendered per frame to improve performance.")
        .defaultValue(512)
        .min(1)
        .sliderRange(1, 4096)
        .build()
    );
    private final Setting<Boolean> onlyRenderLoaded = sgPerformance.add(new BoolSetting.Builder()
        .name("Only Render Loaded Chunks")
        .description("If enabled, only renders chunks that are currently loaded in memory.")
        .defaultValue(false)
        .build()
    );

    // --- End of new settings ---

    // --- Detection Sensitivity Settings ---
    private final Setting<Integer> newChunkThreshold = sgSensitivity.add(new IntSetting.Builder()
        .name("New Chunk Quantifier Threshold (%)")
        .description("Percentage threshold for classifying a chunk as new (default: 51%).")
        .defaultValue(51)
        .sliderRange(1, 100)
        .build()
    );
    private final Setting<Integer> beingUpdatedThreshold = sgSensitivity.add(new IntSetting.Builder()
        .name("Being Updated Quantifier Threshold (%)")
        .description("Percentage threshold for classifying a chunk as being updated (default: 25%).")
        .defaultValue(25)
        .sliderRange(1, 100)
        .build()
    );
    // --- Cache & Persistence Settings ---
    private final Setting<Boolean> removeOnDisable = sgCache.add(new BoolSetting.Builder()
            .name("Remove On Module Disabled")
            .description("Removes cached chunks when disabling the module.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> removeOnWorldLeave = sgCache.add(new BoolSetting.Builder()
            .name("Remove On World Leave/Dimension Change")
            .description("Removes cached chunks when leaving the world or changing dimensions.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> removeOutsideRenderDistance = sgCache.add(new BoolSetting.Builder()
            .name("Remove Outside Render Distance")
            .description("Removes cached chunks when they leave the defined render distance.")
            .defaultValue(false)
            .build()
    );
    private final Setting<Boolean> saveChunkData = sgCdata.add(new BoolSetting.Builder()
            .name("Save Chunk Data")
            .description("Saves cached chunks to a file for persistence across sessions.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> loadChunkData = sgCdata.add(new BoolSetting.Builder()
            .name("Load Chunk Data")
            .description("Loads saved chunks from file on startup.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> autoReloadChunks = sgCdata.add(new BoolSetting.Builder()
            .name("Auto Reload Chunks")
            .description("Automatically reloads chunks from save files on a delay.")
            .defaultValue(false)
            .visible(loadChunkData::get)
            .build()
    );
    private final Setting<Integer> autoReloadDelay = sgCdata.add(new IntSetting.Builder()
            .name("Auto Reload Delay (Seconds)")
            .description("Delay in seconds for auto-reloading chunks from save files.")
            .sliderRange(1, 300)
            .defaultValue(60)
            .visible(() -> autoReloadChunks.get() && loadChunkData.get())
            .build()
    );
    // --- Rendering Settings ---
    public final Setting<Integer> renderDistance = sgRender.add(new IntSetting.Builder()
            .name("Render Distance (Chunks)")
            .description("How many chunks from the player to render detected chunks.")
            .defaultValue(64)
            .min(6)
            .sliderRange(6, 1024)
            .build()
    );
    public final Setting<Integer> renderHeight = sgRender.add(new IntSetting.Builder()
            .name("Render Height")
            .description("The Y-level at which detected chunks will be rendered.")
            .defaultValue(0)
            .sliderRange(-112, 319)
            .build()
    );
    private final Setting<ShapeMode> shapeMode = sgRender.add(new EnumSetting.Builder<ShapeMode>()
            .name("Shape Mode")
            .description("How chunk shapes are rendered: Sides, Lines, or Both.")
            .defaultValue(ShapeMode.Both)
            .build()
    );

    // --- Enhanced Color Settings ---
    private final Setting<Double> colorIntensity = sgRender.add(new DoubleSetting.Builder()
            .name("Color Intensity")
            .description("Multiplier for all chunk color alpha values (brightness).")
            .defaultValue(1.0)
            .min(0.1)
            .max(3.0)
            .sliderMax(3.0)
            .build()
    );
    private final Setting<Boolean> enhancedVisibility = sgRender.add(new BoolSetting.Builder()
            .name("Enhanced Visibility")
            .description("Use brighter, more contrasting colors for better visibility.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> fadeWithDistance = sgRender.add(new BoolSetting.Builder()
            .name("Fade With Distance")
            .description("Gradually fade chunk colors based on distance from player.")
            .defaultValue(false)
            .build()
    );
    private final Setting<Integer> boxHeight = sgRender.add(new IntSetting.Builder()
            .name("Box Height")
            .description("Height of the rendered chunk boxes in blocks.")
            .defaultValue(1)
            .min(1)
            .max(10)
            .sliderMax(10)
            .build()
    );
    private final Setting<Boolean> showColorLegend = sgRender.add(new BoolSetting.Builder()
            .name("Show Color Legend")
            .description("Display a legend showing what each color represents.")
            .defaultValue(false)
            .build()
    );

    // Webhook Settings
    private final Setting<Boolean> enableWebhooks = sgNotifications.add(new BoolSetting.Builder()
            .name("Enable Webhooks")
            .description("Enable Discord webhook notifications for chunk detection.")
            .defaultValue(false)
            .build()
    );

    private final Setting<String> webhookUrl = sgNotifications.add(new StringSetting.Builder()
            .name("Webhook URL")
            .description("Discord webhook URL for notifications.")
            .defaultValue("")
            .visible(enableWebhooks::get)
            .build()
    );

    private final Setting<String> discordPingId = sgNotifications.add(new StringSetting.Builder()
            .name("Discord Ping ID")
            .description("Discord user ID to ping (optional).")
            .defaultValue("")
            .visible(enableWebhooks::get)
            .build()
    );

    private final Setting<Boolean> onlyImportantChunks = sgNotifications.add(new BoolSetting.Builder()
            .name("Only Important Chunks")
            .description("Only send webhooks for NEW and TICK_EXPLOIT chunks.")
            .defaultValue(true)
            .visible(enableWebhooks::get)
            .build()
    );

    private final Setting<Integer> webhookCooldown = sgNotifications.add(new IntSetting.Builder()
            .name("Webhook Cooldown")
            .description("Minimum seconds between webhook notifications.")
            .defaultValue(5)
            .min(0)
            .max(60)
            .sliderMax(60)
            .visible(enableWebhooks::get)
            .build()
    );

    // --- Chunk State Sets ---
    private final Set<ChunkPos> newChunks = Collections.synchronizedSet(new HashSet<>());
    private final Set<ChunkPos> oldChunks = Collections.synchronizedSet(new HashSet<>());

    /**
     * Returns the set of old chunk positions detected by Trails.
     * This set is thread-safe and may be updated in real time.
     */
    public static Set<ChunkPos> getOldChunks() {
        // This assumes a singleton instance or static context; adjust if needed for your architecture.
        // If oldChunks is not static, you may need to access it via the module manager or instance.
        return ((Trails) meteordevelopment.meteorclient.systems.modules.Modules.get().get(Trails.class)).oldChunks;
    }
    private final Set<ChunkPos> beingUpdatedOldChunks = Collections.synchronizedSet(new HashSet<>());
    private final Set<ChunkPos> OldGenerationOldChunks = Collections.synchronizedSet(new HashSet<>());
    private final Set<ChunkPos> tickexploitChunks = Collections.synchronizedSet(new HashSet<>());

    // --- Missing static block sets from NewerNewChunks ---
    public static final Set<Block> ORE_BLOCKS = new HashSet<>();
    static {
        ORE_BLOCKS.add(Blocks.COAL_ORE);
        ORE_BLOCKS.add(Blocks.DEEPSLATE_COAL_ORE);
        ORE_BLOCKS.add(Blocks.COPPER_ORE);
        ORE_BLOCKS.add(Blocks.DEEPSLATE_COPPER_ORE);
        ORE_BLOCKS.add(Blocks.IRON_ORE);
        ORE_BLOCKS.add(Blocks.DEEPSLATE_IRON_ORE);
        ORE_BLOCKS.add(Blocks.GOLD_ORE);
        ORE_BLOCKS.add(Blocks.DEEPSLATE_GOLD_ORE);
        ORE_BLOCKS.add(Blocks.LAPIS_ORE);
        ORE_BLOCKS.add(Blocks.DEEPSLATE_LAPIS_ORE);
        ORE_BLOCKS.add(Blocks.DIAMOND_ORE);
        ORE_BLOCKS.add(Blocks.DEEPSLATE_DIAMOND_ORE);
        ORE_BLOCKS.add(Blocks.REDSTONE_ORE);
        ORE_BLOCKS.add(Blocks.DEEPSLATE_REDSTONE_ORE);
        ORE_BLOCKS.add(Blocks.EMERALD_ORE);
        ORE_BLOCKS.add(Blocks.DEEPSLATE_EMERALD_ORE);
    }
    
    public static final Set<Block> DEEPSLATE_BLOCKS = new HashSet<>();
    static {
        DEEPSLATE_BLOCKS.add(Blocks.DEEPSLATE);
        DEEPSLATE_BLOCKS.add(Blocks.DEEPSLATE_COPPER_ORE);
        DEEPSLATE_BLOCKS.add(Blocks.DEEPSLATE_IRON_ORE);
        DEEPSLATE_BLOCKS.add(Blocks.DEEPSLATE_COAL_ORE);
        DEEPSLATE_BLOCKS.add(Blocks.DEEPSLATE_REDSTONE_ORE);
        DEEPSLATE_BLOCKS.add(Blocks.DEEPSLATE_EMERALD_ORE);
        DEEPSLATE_BLOCKS.add(Blocks.DEEPSLATE_GOLD_ORE);
        DEEPSLATE_BLOCKS.add(Blocks.DEEPSLATE_LAPIS_ORE);
        DEEPSLATE_BLOCKS.add(Blocks.DEEPSLATE_DIAMOND_ORE);
    }
    
    public static final Set<Block> NEW_OVERWORLD_BLOCKS = new HashSet<>();
    static {
        NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.AMETHYST_BLOCK);
        NEW_OVERWORLD_BLOCKS.add(Blocks.BUDDING_AMETHYST);
        NEW_OVERWORLD_BLOCKS.add(Blocks.AZALEA);
        NEW_OVERWORLD_BLOCKS.add(Blocks.FLOWERING_AZALEA);
        NEW_OVERWORLD_BLOCKS.add(Blocks.BIG_DRIPLEAF);
        NEW_OVERWORLD_BLOCKS.add(Blocks.BIG_DRIPLEAF_STEM);
        NEW_OVERWORLD_BLOCKS.add(Blocks.SMALL_DRIPLEAF);
        NEW_OVERWORLD_BLOCKS.add(Blocks.CAVE_VINES);
        NEW_OVERWORLD_BLOCKS.add(Blocks.CAVE_VINES_PLANT);
        NEW_OVERWORLD_BLOCKS.add(Blocks.SPORE_BLOSSOM);
        NEW_OVERWORLD_BLOCKS.add(Blocks.COPPER_ORE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_COPPER_ORE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_IRON_ORE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_COAL_ORE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_REDSTONE_ORE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_EMERALD_ORE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_GOLD_ORE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_LAPIS_ORE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_DIAMOND_ORE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.GLOW_LICHEN);
        NEW_OVERWORLD_BLOCKS.add(Blocks.RAW_COPPER_BLOCK);
        NEW_OVERWORLD_BLOCKS.add(Blocks.RAW_IRON_BLOCK);
        NEW_OVERWORLD_BLOCKS.add(Blocks.DRIPSTONE_BLOCK);
        NEW_OVERWORLD_BLOCKS.add(Blocks.MOSS_BLOCK);
        NEW_OVERWORLD_BLOCKS.add(Blocks.MOSS_CARPET);
        NEW_OVERWORLD_BLOCKS.add(Blocks.POINTED_DRIPSTONE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.SMOOTH_BASALT);
        NEW_OVERWORLD_BLOCKS.add(Blocks.TUFF);
        NEW_OVERWORLD_BLOCKS.add(Blocks.CALCITE);
        NEW_OVERWORLD_BLOCKS.add(Blocks.HANGING_ROOTS);
        NEW_OVERWORLD_BLOCKS.add(Blocks.ROOTED_DIRT);
        NEW_OVERWORLD_BLOCKS.add(Blocks.AZALEA_LEAVES);
        NEW_OVERWORLD_BLOCKS.add(Blocks.FLOWERING_AZALEA_LEAVES);
        NEW_OVERWORLD_BLOCKS.add(Blocks.POWDER_SNOW);
    }
    
    public static final Set<Block> NEW_NETHER_BLOCKS = new HashSet<>();
    static {
        NEW_NETHER_BLOCKS.add(Blocks.ANCIENT_DEBRIS);
        NEW_NETHER_BLOCKS.add(Blocks.BASALT);
        NEW_NETHER_BLOCKS.add(Blocks.BLACKSTONE);
        NEW_NETHER_BLOCKS.add(Blocks.GILDED_BLACKSTONE);
        NEW_NETHER_BLOCKS.add(Blocks.POLISHED_BLACKSTONE_BRICKS);
        NEW_NETHER_BLOCKS.add(Blocks.CRIMSON_STEM);
        NEW_NETHER_BLOCKS.add(Blocks.CRIMSON_NYLIUM);
        NEW_NETHER_BLOCKS.add(Blocks.NETHER_GOLD_ORE);
        NEW_NETHER_BLOCKS.add(Blocks.WARPED_NYLIUM);
        NEW_NETHER_BLOCKS.add(Blocks.WARPED_STEM);
        NEW_NETHER_BLOCKS.add(Blocks.TWISTING_VINES);
        NEW_NETHER_BLOCKS.add(Blocks.WEEPING_VINES);
        NEW_NETHER_BLOCKS.add(Blocks.BONE_BLOCK);
        NEW_NETHER_BLOCKS.add(Blocks.CHAIN);
        NEW_NETHER_BLOCKS.add(Blocks.OBSIDIAN);
        NEW_NETHER_BLOCKS.add(Blocks.CRYING_OBSIDIAN);
        NEW_NETHER_BLOCKS.add(Blocks.SOUL_SOIL);
        NEW_NETHER_BLOCKS.add(Blocks.SOUL_FIRE);
    }

    // --- Other fields ---
    private int deletewarningTicks=666;
    private int deletewarning=0;
    private String serverip;
    private String world;
    private boolean worldchange=false;
    private int autoreloadticks=0;
    private int loadingticks=0;
    private int justenabledsavedata=0;
    private boolean saveDataWasOn = false;
    private int errticks=0;
    private long lastWebhookTime = 0;

    public Trails() {
        super(Skylandia.Hunting, "Trails", "Visualizes new, old, and updated chunks using advanced detection logic.");
    }

    @Override
    public void onActivate() {
        if (saveChunkData.get()) saveDataWasOn = true;
        else if (!saveChunkData.get()) saveDataWasOn = false;
        if (autoReloadChunks.get()) {
            clearChunkData();
        }
        if (saveChunkData.get() || loadChunkData.get() && mc.world != null) {
            world = mc.world.getRegistryKey().getValue().toString().replace(':', '_');
            if (mc.isInSingleplayer()) {
                String[] array = mc.getServer().getSavePath(net.minecraft.util.WorldSavePath.ROOT).toString().replace(':', '_').split("/|\\\\");
                serverip = array[array.length - 2];
            } else {
                serverip = mc.getCurrentServerEntry().address.replace(':', '_');
            }
        }
        if (saveChunkData.get()) {
            try {
                java.nio.file.Files.createDirectories(java.nio.file.Paths.get("TrouserStreak", "NewChunks", serverip, world));
            } catch (java.io.IOException e) {
                e.printStackTrace();
            }
        }
        if (saveChunkData.get() || loadChunkData.get()) {
            java.nio.file.Path baseDir = java.nio.file.Paths.get("TrouserStreak", "NewChunks", serverip, world);
            for (java.nio.file.Path fileName : java.util.Set.of(
                    java.nio.file.Paths.get("OldChunkData.txt"),
                    java.nio.file.Paths.get("BeingUpdatedChunkData.txt"),
                    java.nio.file.Paths.get("OldGenerationChunkData.txt"),
                    java.nio.file.Paths.get("NewChunkData.txt"),
                    java.nio.file.Paths.get("BlockExploitChunkData.txt")
            )) {
                java.nio.file.Path fullPath = baseDir.resolve(fileName);
                try {
                    java.nio.file.Files.createDirectories(fullPath.getParent());
                    if (java.nio.file.Files.notExists(fullPath)) {
                        java.nio.file.Files.createFile(fullPath);
                    }
                } catch (java.io.IOException e) {
                    e.printStackTrace();
                }
            }
        }
        if (loadChunkData.get()) {
            loadData();
        }
        autoreloadticks = 0;
        loadingticks = 0;
        worldchange = false;
        justenabledsavedata = 0;
    }

    @Override
    public void onDeactivate() {
        autoreloadticks = 0;
        loadingticks = 0;
        worldchange = false;
        justenabledsavedata = 0;
        if (removeOnDisable.get() | autoReloadChunks.get()) {
            clearChunkData();
        }
        super.onDeactivate();
    }

    private void clearChunkData() {
        newChunks.clear();
        oldChunks.clear();
        beingUpdatedOldChunks.clear();
        OldGenerationOldChunks.clear();
        tickexploitChunks.clear();
    }

    // Removed tick-based chunk classification - chunks are now only processed via packet events
    // This preserves chunks across ticks and matches the behavior of NewerNewChunks

    @EventHandler
    private void onRender(Render3DEvent event) {
        if (mc.player == null) return;

        // Display color legend if enabled
        renderColorLegend(event);

        BlockPos playerPos = new BlockPos(mc.player.getBlockX(), renderHeight.get(), mc.player.getBlockZ());
        int playerChunkX = mc.player.getBlockX() >> 4;
        int playerChunkZ = mc.player.getBlockZ() >> 4;
        int r = renderDistance.get();

        int chunksRendered = 0;
        int maxChunks = maxChunksPerFrame.get();

        for (int dx = -r; dx <= r && chunksRendered < maxChunks; dx++) {
            for (int dz = -r; dz <= r && chunksRendered < maxChunks; dz++) {
                int chunkX = playerChunkX + dx;
                int chunkZ = playerChunkZ + dz;
                ChunkPos c = new ChunkPos(chunkX, chunkZ);

                // Only render if within render distance in blocks
                if (!playerPos.isWithinDistance(new BlockPos(c.getCenterX(), renderHeight.get(), c.getCenterZ()), r * 16)) continue;

                // Check if chunk should only render when loaded
                if (onlyRenderLoaded.get() && (mc.world == null || mc.world.getChunkManager().getChunk(chunkX, chunkZ) == null)) continue;

                ChunkType chunkType = null;
                SettingColor side = null, line = null;
                ShapeMode mode = shapeMode.get();

                // Determine chunk type and get colors/settings
                if (newChunks.contains(c)) {
                    chunkType = ChunkType.NEW;
                } else if (tickexploitChunks.contains(c)) {
                    if (detectMode.get() == DetectMode.BlockExploitMode && blockUpdateExploit.get()) {
                        chunkType = ChunkType.TICK_EXPLOIT;
                    } else if ((detectMode.get() == DetectMode.Normal) && blockUpdateExploit.get()) {
                        chunkType = ChunkType.NEW; // Treat as NEW in normal mode
                    } else if ((detectMode.get() == DetectMode.IgnoreBlockExploit) && blockUpdateExploit.get()) {
                        chunkType = ChunkType.OLD; // Treat as OLD when ignoring exploit
                    } else if (!blockUpdateExploit.get()) {
                        chunkType = ChunkType.OLD; // Treat as OLD when exploit disabled
                    }
                } else if (oldChunks.contains(c)) {
                    chunkType = ChunkType.OLD;
                } else if (beingUpdatedOldChunks.contains(c)) {
                    chunkType = ChunkType.BEING_UPDATED;
                } else if (OldGenerationOldChunks.contains(c)) {
                    chunkType = ChunkType.OLD_GENERATION;
                }

                // Get colors and settings for the determined chunk type
                if (chunkType != null) {
                    // Check if rendering is enabled for this chunk type
                    if (!renderToggles.get(chunkType).get()) continue;

                    side = sideColors.get(chunkType).get();
                    line = lineColors.get(chunkType).get();
                    mode = shapeModes.get(chunkType).get();

                    // Apply color enhancements
                    side = enhanceColor(side, c, playerPos);
                    line = enhanceColor(line, c, playerPos);

                    // Only render if color is set and alpha > 5
                    if ((side.a > 5 || line.a > 5)) {
                        int boxHeightValue = boxHeight.get();
                        event.renderer.box(
                            c.getStartX(), renderHeight.get(), c.getStartZ(),
                            c.getStartX() + 16, renderHeight.get() + boxHeightValue, c.getStartZ() + 16,
                            side,
                            line,
                            mode,
                            0
                        );
                        chunksRendered++;
                    }
                }
            }
        }
    }

    // --- Color enhancement helper ---
    private SettingColor enhanceColor(SettingColor originalColor, ChunkPos chunkPos, BlockPos playerPos) {
        if (originalColor == null) return originalColor;

        int r = originalColor.r;
        int g = originalColor.g;
        int b = originalColor.b;
        int a = originalColor.a;

        // Apply color intensity multiplier
        double intensity = colorIntensity.get();
        a = (int) Math.min(255, a * intensity);

        // Apply enhanced visibility if enabled
        if (enhancedVisibility.get()) {
            // Boost saturation and brightness for better visibility
            // Convert to HSV, enhance saturation and value, convert back
            float[] hsv = new float[3];
            java.awt.Color.RGBtoHSB(r, g, b, hsv);

            // Increase saturation and brightness
            hsv[1] = Math.min(1.0f, hsv[1] * 1.2f); // Boost saturation by 20%
            hsv[2] = Math.min(1.0f, hsv[2] * 1.1f); // Boost brightness by 10%

            java.awt.Color enhanced = java.awt.Color.getHSBColor(hsv[0], hsv[1], hsv[2]);
            r = enhanced.getRed();
            g = enhanced.getGreen();
            b = enhanced.getBlue();
        }

        // Apply distance-based fading if enabled
        if (fadeWithDistance.get()) {
            double distance = Math.sqrt(playerPos.getSquaredDistance(chunkPos.getCenterX(), playerPos.getY(), chunkPos.getCenterZ()));
            double maxDistance = renderDistance.get() * 16.0;
            double fadeRatio = 1.0 - (distance / maxDistance);
            fadeRatio = Math.max(0.3, fadeRatio); // Minimum 30% visibility
            a = (int) (a * fadeRatio);
        }

        return new SettingColor(r, g, b, Math.max(5, Math.min(255, a)));
    }

    // --- Color legend display ---
    private void renderColorLegend(Render3DEvent event) {
        if (!showColorLegend.get() || mc.player == null) return;

        // Display legend in chat periodically
        if (mc.world.getTime() % 200 == 0) { // Every 10 seconds
            info("§7[Trails Color Legend]");
            info("§c■ §fNEW chunks (recently generated)");
            info("§a■ §fOLD chunks (pre-existing)");
            info("§6■ §fBEING_UPDATED chunks (updating from old version)");
            info("§e■ §fOLD_GENERATION chunks (old generation detected)");
            info("§5■ §fTICK_EXPLOIT chunks (block update exploit)");
            info("§7■ §fUNKNOWN chunks (unclassified)");
        }
    }

    // --- Notification logic per chunk type ---
    private void notifyChunkEvent(ChunkType type, ChunkPos pos) {
        // Check if notifications are enabled for this chunk type
        if (notificationToggles.get(type) == null || !notificationToggles.get(type).get()) {
            return;
        }

        // Send chat notification
        String msg = "[Trails] " + type.name() + " chunk detected at " + pos.x + ", " + pos.z;
        info(msg);

        // Send webhook notification if enabled
        if (enableWebhooks.get() && !webhookUrl.get().isEmpty()) {
            // Check if we should only send important chunks
            if (onlyImportantChunks.get()) {
                if (type != ChunkType.NEW && type != ChunkType.TICK_EXPLOIT) {
                    return; // Skip non-important chunk types
                }
            }

            // Check cooldown
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastWebhookTime < webhookCooldown.get() * 1000L) {
                return; // Still in cooldown
            }

            try {
                String playerName = mc.player != null ? mc.player.getGameProfile().getName() : "Unknown";
                sendWebhook(
                    webhookUrl.get(),
                    "Trails - " + type.name() + " Chunk",
                    "Chunk detected at " + pos.x + ", " + pos.z,
                    discordPingId.get().isEmpty() ? null : discordPingId.get(),
                    playerName
                );
                lastWebhookTime = currentTime; // Update last webhook time
            } catch (Exception e) {
                error("Failed to send webhook: " + e.getMessage());
            }
        }
    }

    // --- Chunk classification and notification helper ---
    private void classifyAndNotifyChunk(ChunkPos pos, ChunkType type) {
        // Add to appropriate set
        switch (type) {
            case NEW:
                if (!newChunks.contains(pos) && !oldChunks.contains(pos) && !beingUpdatedOldChunks.contains(pos) && !OldGenerationOldChunks.contains(pos) && !tickexploitChunks.contains(pos)) {
                    newChunks.add(pos);
                    notifyChunkEvent(type, pos);
                    if (saveChunkData.get()) {
                        saveData(java.nio.file.Paths.get("NewChunkData.txt"), pos);
                    }
                }
                break;
            case OLD:
                if (!oldChunks.contains(pos) && !newChunks.contains(pos) && !beingUpdatedOldChunks.contains(pos) && !OldGenerationOldChunks.contains(pos) && !tickexploitChunks.contains(pos)) {
                    oldChunks.add(pos);
                    notifyChunkEvent(type, pos);
                    if (saveChunkData.get()) {
                        saveData(java.nio.file.Paths.get("OldChunkData.txt"), pos);
                    }
                }
                break;
            case BEING_UPDATED:
                if (!beingUpdatedOldChunks.contains(pos) && !newChunks.contains(pos) && !oldChunks.contains(pos) && !OldGenerationOldChunks.contains(pos) && !tickexploitChunks.contains(pos)) {
                    beingUpdatedOldChunks.add(pos);
                    notifyChunkEvent(type, pos);
                    if (saveChunkData.get()) {
                        saveData(java.nio.file.Paths.get("BeingUpdatedChunkData.txt"), pos);
                    }
                }
                break;
            case OLD_GENERATION:
                if (!OldGenerationOldChunks.contains(pos) && !newChunks.contains(pos) && !oldChunks.contains(pos) && !beingUpdatedOldChunks.contains(pos) && !tickexploitChunks.contains(pos)) {
                    OldGenerationOldChunks.add(pos);
                    notifyChunkEvent(type, pos);
                    if (saveChunkData.get()) {
                        saveData(java.nio.file.Paths.get("OldGenerationChunkData.txt"), pos);
                    }
                }
                break;
            case TICK_EXPLOIT:
                if (!tickexploitChunks.contains(pos) && !newChunks.contains(pos) && !oldChunks.contains(pos) && !beingUpdatedOldChunks.contains(pos) && !OldGenerationOldChunks.contains(pos)) {
                    tickexploitChunks.add(pos);
                    notifyChunkEvent(type, pos);
                    if (saveChunkData.get()) {
                        saveData(java.nio.file.Paths.get("BlockExploitChunkData.txt"), pos);
                    }
                }
                break;
        }
    }

    private void renderBox(ChunkPos c, SettingColor sides, SettingColor lines, Render3DEvent event) {
        double x1 = c.getStartX();
        double z1 = c.getStartZ();
        double y = renderHeight.get();
        double x2 = x1 + 16;
        double z2 = z1 + 16;
        event.renderer.box(
                x1, y, z1,
                x2, y + 1, z2,
                sides,
                lines,
                shapeMode.get(),
                0
        );
    }

    // --- Advanced packet and chunk data event handling logic (ported from NewerNewChunks) ---
    private void loadData() {
        loadChunkData(java.nio.file.Paths.get("BlockExploitChunkData.txt"), tickexploitChunks);
        loadChunkData(java.nio.file.Paths.get("OldChunkData.txt"), oldChunks);
        loadChunkData(java.nio.file.Paths.get("NewChunkData.txt"), newChunks);
        loadChunkData(java.nio.file.Paths.get("BeingUpdatedChunkData.txt"), beingUpdatedOldChunks);
        loadChunkData(java.nio.file.Paths.get("OldGenerationChunkData.txt"), OldGenerationOldChunks);
    }

    private void loadChunkData(java.nio.file.Path savedDataLocation, Set<ChunkPos> chunkSet) {
        try {
            java.nio.file.Path filePath = java.nio.file.Paths.get("TrouserStreak/NewChunks", serverip, world).resolve(savedDataLocation);
            java.util.List<String> allLines = java.nio.file.Files.readAllLines(filePath);

            for (String line : allLines) {
                if (line != null && !line.isEmpty()) {
                    String[] array = line.split(", ");
                    if (array.length == 2) {
                        int X = Integer.parseInt(array[0].replaceAll("\\[", "").replaceAll("\\]", ""));
                        int Z = Integer.parseInt(array[1].replaceAll("\\[", "").replaceAll("\\]", ""));
                        ChunkPos chunkPos = new ChunkPos(X, Z);
                        if (!OldGenerationOldChunks.contains(chunkPos) && !beingUpdatedOldChunks.contains(chunkPos) && !tickexploitChunks.contains(chunkPos) && !newChunks.contains(chunkPos) && !oldChunks.contains(chunkPos)) {
                            chunkSet.add(chunkPos);
                        }
                    }
                }
            }
        } catch (java.io.IOException e) {
            e.printStackTrace();
        }
    }

    private void saveData(java.nio.file.Path savedDataLocation, ChunkPos chunkpos) {
        try {
            java.nio.file.Path dirPath = java.nio.file.Paths.get("TrouserStreak", "NewChunks", serverip, world);
            java.nio.file.Files.createDirectories(dirPath);

            java.nio.file.Path filePath = dirPath.resolve(savedDataLocation);
            String data = chunkpos.toString() + System.lineSeparator();

            java.nio.file.Files.write(filePath, data.getBytes(java.nio.charset.StandardCharsets.UTF_8),
                    java.nio.file.StandardOpenOption.CREATE,
                    java.nio.file.StandardOpenOption.APPEND);
        } catch (java.io.IOException e) {
            e.printStackTrace();
        }
    }

    // --- Event Handlers Ported from NewerNewChunks ---

    @meteordevelopment.orbit.EventHandler
    private void onScreenOpen(meteordevelopment.meteorclient.events.game.OpenScreenEvent event) {
        if (event.screen instanceof net.minecraft.client.gui.screen.DisconnectedScreen) {
            if (removeOnWorldLeave.get()) {
                clearChunkData();
            }
        }
        if (event.screen instanceof net.minecraft.client.gui.screen.DownloadingTerrainScreen) {
            worldchange = true;
        }
    }

    @meteordevelopment.orbit.EventHandler
    private void onGameLeft(meteordevelopment.meteorclient.events.game.GameLeftEvent event) {
        if (removeOnWorldLeave.get()) {
            clearChunkData();
        }
    }

    @meteordevelopment.orbit.EventHandler
    private void onPreTick(meteordevelopment.meteorclient.events.world.TickEvent.Pre event) {
        if (mc.world == null) return;
        world = mc.world.getRegistryKey().getValue().toString().replace(':', '_');

        if (deletewarningTicks <= 100) deletewarningTicks++;
        else deletewarning = 0;
        if (deletewarning >= 2) {
            if (mc.isInSingleplayer()) {
                String[] array = mc.getServer().getSavePath(net.minecraft.util.WorldSavePath.ROOT).toString().replace(':', '_').split("/|\\\\");
                serverip = array[array.length - 2];
            } else {
                serverip = mc.getCurrentServerEntry().address.replace(':', '_');
            }
            clearChunkData();
            try {
                java.nio.file.Files.deleteIfExists(java.nio.file.Paths.get("TrouserStreak", "NewChunks", serverip, world, "NewChunkData.txt"));
                java.nio.file.Files.deleteIfExists(java.nio.file.Paths.get("TrouserStreak", "NewChunks", serverip, world, "OldChunkData.txt"));
                java.nio.file.Files.deleteIfExists(java.nio.file.Paths.get("TrouserStreak", "NewChunks", serverip, world, "BeingUpdatedChunkData.txt"));
                java.nio.file.Files.deleteIfExists(java.nio.file.Paths.get("TrouserStreak", "NewChunks", serverip, world, "OldGenerationChunkData.txt"));
                java.nio.file.Files.deleteIfExists(java.nio.file.Paths.get("TrouserStreak", "NewChunks", serverip, world, "BlockExploitChunkData.txt"));
            } catch (java.io.IOException e) {
                e.printStackTrace();
            }
            deletewarning = 0;
        }

        if (detectMode.get() == DetectMode.Normal && blockUpdateExploit.get()) {
            if (errticks < 6) {
                errticks++;
            }
            if (errticks == 5) {
                error("BlockExploitMode RECOMMENDED. Required to determine false positives from the Block Exploit from the OldChunks.");
            }
        } else errticks = 0;

        if (loadChunkData.get()) {
            if (loadingticks < 1) {
                loadData();
                loadingticks++;
            }
        } else if (!loadChunkData.get()) {
            loadingticks = 0;
        }

        if (autoReloadChunks.get()) {
            autoreloadticks++;
            if (autoreloadticks == autoReloadDelay.get() * 20) {
                clearChunkData();
                if (loadChunkData.get()) {
                    loadData();
                }
            } else if (autoreloadticks >= autoReloadDelay.get() * 20) {
                autoreloadticks = 0;
            }
        }

        if (loadChunkData.get() && worldchange) {
            if (removeOnWorldLeave.get()) {
                clearChunkData();
            }
            loadData();
            worldchange = false;
        }

        if (!saveChunkData.get()) saveDataWasOn = false;
        if (saveChunkData.get() && justenabledsavedata <= 2 && !saveDataWasOn) {
            justenabledsavedata++;
            if (justenabledsavedata == 1) {
                synchronized (newChunks) {
                    for (ChunkPos chunk : newChunks) {
                        saveData(java.nio.file.Paths.get("NewChunkData.txt"), chunk);
                    }
                }
                synchronized (OldGenerationOldChunks) {
                    for (ChunkPos chunk : OldGenerationOldChunks) {
                        saveData(java.nio.file.Paths.get("OldGenerationChunkData.txt"), chunk);
                    }
                }
                synchronized (beingUpdatedOldChunks) {
                    for (ChunkPos chunk : beingUpdatedOldChunks) {
                        saveData(java.nio.file.Paths.get("BeingUpdatedChunkData.txt"), chunk);
                    }
                }
                synchronized (oldChunks) {
                    for (ChunkPos chunk : oldChunks) {
                        saveData(java.nio.file.Paths.get("OldChunkData.txt"), chunk);
                    }
                }
                synchronized (tickexploitChunks) {
                    for (ChunkPos chunk : tickexploitChunks) {
                        saveData(java.nio.file.Paths.get("BlockExploitChunkData.txt"), chunk);
                    }
                }
            }
        }

        if (removeOutsideRenderDistance.get()) removeChunksOutsideRenderDistance();
    }

    private void removeChunksOutsideRenderDistance() {
        if (mc.player == null) return;
        BlockPos playerPos = new BlockPos(mc.player.getBlockX(), renderHeight.get(), mc.player.getBlockZ());
        double renderDistanceBlocks = renderDistance.get() * 16;

        removeChunksOutsideRenderDistance(newChunks, playerPos, renderDistanceBlocks);
        removeChunksOutsideRenderDistance(oldChunks, playerPos, renderDistanceBlocks);
        removeChunksOutsideRenderDistance(beingUpdatedOldChunks, playerPos, renderDistanceBlocks);
        removeChunksOutsideRenderDistance(OldGenerationOldChunks, playerPos, renderDistanceBlocks);
        removeChunksOutsideRenderDistance(tickexploitChunks, playerPos, renderDistanceBlocks);
    }

    private void removeChunksOutsideRenderDistance(Set<ChunkPos> chunkSet, BlockPos playerPos, double renderDistanceBlocks) {
        chunkSet.removeIf(c -> !playerPos.isWithinDistance(new BlockPos(c.getCenterX(), renderHeight.get(), c.getCenterZ()), renderDistanceBlocks));
    }

    @meteordevelopment.orbit.EventHandler
    private void onReadPacket(PacketEvent.Receive event) {
        // Liquid exploit: ChunkDeltaUpdateS2CPacket
        if (event.packet instanceof ChunkDeltaUpdateS2CPacket packet && liquidExploit.get()) {
            packet.visitUpdates((pos, state) -> {
                ChunkPos chunkPos = new ChunkPos(pos);
                if (!state.getFluidState().isEmpty() && !state.getFluidState().isStill()) {
                    for (net.minecraft.util.math.Direction dir : new net.minecraft.util.math.Direction[] {
                        net.minecraft.util.math.Direction.EAST, net.minecraft.util.math.Direction.NORTH,
                        net.minecraft.util.math.Direction.WEST, net.minecraft.util.math.Direction.SOUTH,
                        net.minecraft.util.math.Direction.UP
                    }) {
                        try {
                            if (mc.world != null && mc.world.getBlockState(pos.offset(dir)).getFluidState().isStill() &&
                                (!OldGenerationOldChunks.contains(chunkPos) && !beingUpdatedOldChunks.contains(chunkPos) &&
                                 !newChunks.contains(chunkPos) && !oldChunks.contains(chunkPos))) {
                                tickexploitChunks.remove(chunkPos);
                                classifyAndNotifyChunk(chunkPos, ChunkType.NEW);
                                return;
                            }
                        } catch (Exception e) {}
                    }
                }
            });
        }
        // BlockUpdateS2CPacket
        else if (event.packet instanceof BlockUpdateS2CPacket packet) {
            ChunkPos chunkPos = new ChunkPos(packet.getPos());
            if (blockUpdateExploit.get()) {
                try {
                    if (!OldGenerationOldChunks.contains(chunkPos) && !beingUpdatedOldChunks.contains(chunkPos) &&
                        !tickexploitChunks.contains(chunkPos) && !oldChunks.contains(chunkPos) && !newChunks.contains(chunkPos)) {
                        classifyAndNotifyChunk(chunkPos, ChunkType.TICK_EXPLOIT);
                    }
                } catch (Exception e) {}
            }
            if (!packet.getState().getFluidState().isEmpty() && !packet.getState().getFluidState().isStill() && liquidExploit.get()) {
                for (net.minecraft.util.math.Direction dir : new net.minecraft.util.math.Direction[] {
                    net.minecraft.util.math.Direction.EAST, net.minecraft.util.math.Direction.NORTH,
                    net.minecraft.util.math.Direction.WEST, net.minecraft.util.math.Direction.SOUTH,
                    net.minecraft.util.math.Direction.UP
                }) {
                    try {
                        if (mc.world != null && mc.world.getBlockState(packet.getPos().offset(dir)).getFluidState().isStill() &&
                            (!OldGenerationOldChunks.contains(chunkPos) && !beingUpdatedOldChunks.contains(chunkPos) &&
                             !newChunks.contains(chunkPos) && !oldChunks.contains(chunkPos))) {
                            tickexploitChunks.remove(chunkPos);
                            classifyAndNotifyChunk(chunkPos, ChunkType.NEW);
                            return;
                        }
                    } catch (Exception e) {}
                }
            }
        }
        // Real-time ChunkDataS2CPacket detection logic (ported from NewerNewChunks)
        else if (event.packet instanceof net.minecraft.network.packet.s2c.play.ChunkDataS2CPacket packet && mc.world != null && realTimeDetection.get()) {
            ChunkPos oldpos = new ChunkPos(packet.getChunkX(), packet.getChunkZ());
            
            try {
                var existingChunk = mc.world.getChunkManager().getChunk(packet.getChunkX(), packet.getChunkZ());
                if (existingChunk == null) {
                    net.minecraft.world.chunk.WorldChunk chunk = new net.minecraft.world.chunk.WorldChunk(mc.world, oldpos);
                    try {
                        // Use chunk data loading from packet
                        net.minecraft.nbt.NbtCompound heightmaps = new net.minecraft.nbt.NbtCompound();
                        java.util.concurrent.CompletableFuture.runAsync(() -> {
                            chunk.loadFromPacket(packet.getChunkData().getSectionsDataBuf(), heightmaps,
                                    packet.getChunkData().getBlockEntities(packet.getChunkX(), packet.getChunkZ()));
                        }).join();
                    } catch (Exception e) {}
                    
                    boolean isNewChunk = false;
                    boolean isOldGeneration = false;
                    boolean chunkIsBeingUpdated = false;
                    boolean foundAnyOre = false;
                    boolean isNewOverworldGeneration = false;
                    boolean isNewNetherGeneration = false;
                    net.minecraft.world.chunk.ChunkSection[] sections = chunk.getSectionArray();
                    
                    // Overworld old chunk detection
                    if (overworldOldChunksDetector.get() && mc.world.getRegistryKey() == net.minecraft.world.World.OVERWORLD && chunk.getStatus().isAtLeast(net.minecraft.world.chunk.ChunkStatus.FULL) && !chunk.isEmpty()) {
                        for (int i = 0; i < 17; i++) {
                            net.minecraft.world.chunk.ChunkSection section = sections[i];
                            if (section != null && !section.isEmpty()) {
                                for (int x = 0; x < 16; x++) {
                                    for (int y = 0; y < 16; y++) {
                                        for (int z = 0; z < 16; z++) {
                                            if (!foundAnyOre && ORE_BLOCKS.contains(section.getBlockState(x, y, z).getBlock())) foundAnyOre = true;
                                            if (((y >= 5 && i == 4) || i > 4) && !isNewOverworldGeneration && (NEW_OVERWORLD_BLOCKS.contains(section.getBlockState(x, y, z).getBlock()) || DEEPSLATE_BLOCKS.contains(section.getBlockState(x, y, z).getBlock()))) {
                                                isNewOverworldGeneration = true;
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (foundAnyOre && !isOldGeneration && !isNewOverworldGeneration) isOldGeneration = true;
                    }
                    
                    // Nether old chunk detection
                    if (netherOldChunksDetector.get() && mc.world.getRegistryKey() == net.minecraft.world.World.NETHER && chunk.getStatus().isAtLeast(net.minecraft.world.chunk.ChunkStatus.FULL) && !chunk.isEmpty()) {
                        for (int i = 0; i < 8; i++) {
                            net.minecraft.world.chunk.ChunkSection section = sections[i];
                            if (section != null && !section.isEmpty()) {
                                for (int x = 0; x < 16; x++) {
                                    for (int y = 0; y < 16; y++) {
                                        for (int z = 0; z < 16; z++) {
                                            if (!isNewNetherGeneration && NEW_NETHER_BLOCKS.contains(section.getBlockState(x, y, z).getBlock())) {
                                                isNewNetherGeneration = true;
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (!isOldGeneration && !isNewNetherGeneration) isOldGeneration = true;
                    }
                    
                    // End old chunk detection
                    if (endOldChunksDetector.get() && mc.world.getRegistryKey() == net.minecraft.world.World.END && chunk.getStatus().isAtLeast(net.minecraft.world.chunk.ChunkStatus.FULL) && !chunk.isEmpty()) {
                        net.minecraft.world.chunk.ChunkSection section = chunk.getSection(0);
                        var biomesContainer = section.getBiomeContainer();
                        // Check biomes by accessing the biome at specific positions
                        var biome = biomesContainer.get(0, 0, 0);
                        if (biome.toString().contains("the_end")) {
                            isOldGeneration = true;
                        }
                    }
                    
                    // Palette exploit and being updated logic
                    if (paletteExploit.get()) {
                        boolean firstchunkappearsnew = false;
                        int loops = 0;
                        int newChunkQuantifier = 0;
                        int oldChunkQuantifier = 0;
                        try {
                            for (net.minecraft.world.chunk.ChunkSection section : sections) {
                                if (section != null) {
                                    int isNewSection = 0;
                                    int isBeingUpdatedSection = 0;
                                    
                                    if (!section.isEmpty()) {
                                        var blockStatesContainer = section.getBlockStateContainer();
                                        // Use correct palette access method from working NewerNewChunks
                                        try {
                                            var blockStatePalette = blockStatesContainer.data.palette();
                                            int blockPaletteLength = blockStatePalette.getSize();
                                        
                                        for (int i2 = 0; i2 < blockPaletteLength; i2++) {
                                            var blockPaletteEntry = blockStatePalette.get(i2);
                                            if (i2 == 0 && loops == 0 && blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.AIR && mc.world.getRegistryKey() != net.minecraft.world.World.END)
                                                firstchunkappearsnew = true;
                                            if (i2 == 0 && blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.AIR && mc.world.getRegistryKey() != net.minecraft.world.World.NETHER && mc.world.getRegistryKey() != net.minecraft.world.World.END)
                                                isNewSection++;
                                            if (i2 == 1 && (blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.WATER || blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.STONE || blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.GRASS_BLOCK || blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.SNOW_BLOCK) && mc.world.getRegistryKey() != net.minecraft.world.World.NETHER && mc.world.getRegistryKey() != net.minecraft.world.World.END)
                                                isNewSection++;
                                            if (i2 == 2 && (blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.SNOW_BLOCK || blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.DIRT || blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.POWDER_SNOW) && mc.world.getRegistryKey() != net.minecraft.world.World.NETHER && mc.world.getRegistryKey() != net.minecraft.world.World.END)
                                                isNewSection++;
                                            if (loops == 4 && blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.BEDROCK && mc.world.getRegistryKey() != net.minecraft.world.World.NETHER && mc.world.getRegistryKey() != net.minecraft.world.World.END) {
                                                if (!chunkIsBeingUpdated && beingUpdatedDetector.get())
                                                    chunkIsBeingUpdated = true;
                                            }
                                            if (blockPaletteEntry.getBlock() == net.minecraft.block.Blocks.AIR && (mc.world.getRegistryKey() == net.minecraft.world.World.NETHER || mc.world.getRegistryKey() == net.minecraft.world.World.END))
                                                isBeingUpdatedSection++;
                                        }
                                        if (isBeingUpdatedSection >= 2) oldChunkQuantifier++;
                                        if (isNewSection >= 2) newChunkQuantifier++;
                                        } catch (Exception e) {
                                            // Fallback if palette access fails - use simple heuristic
                                            var firstBlock = blockStatesContainer.get(0, 0, 0);
                                            if (firstBlock.getBlock() == net.minecraft.block.Blocks.AIR) {
                                                isNewSection += 2;
                                                newChunkQuantifier++;
                                            }
                                        }
                                    }
                                    if (mc.world.getRegistryKey() == net.minecraft.world.World.END) {
                                        var biomesContainer = section.getBiomeContainer();
                                        // Check biomes by accessing the biome at specific positions
                                        var biome = biomesContainer.get(0, 0, 0);
                                        if (biome.toString().contains("plains")) isNewChunk = true;
                                        if (!isNewChunk && !biome.toString().contains("the_end")) isNewChunk = false;
                                    }
                                    if (!section.isEmpty()) loops++;
                                }
                            }
                            
                            if (loops > 0) {
                                if (beingUpdatedDetector.get() && (mc.world.getRegistryKey() == net.minecraft.world.World.NETHER || mc.world.getRegistryKey() == net.minecraft.world.World.END)){
                                    double oldpercentage = ((double) oldChunkQuantifier / loops) * 100;
                                    if (oldpercentage >= beingUpdatedThreshold.get()) chunkIsBeingUpdated = true;
                                }
                                else if (mc.world.getRegistryKey() != net.minecraft.world.World.NETHER && mc.world.getRegistryKey() != net.minecraft.world.World.END){
                                    double percentage = ((double) newChunkQuantifier / loops) * 100;
                                    if (percentage >= newChunkThreshold.get()) isNewChunk = true;
                                }
                            }
                        } catch (Exception e) {
                            if (beingUpdatedDetector.get() && (mc.world.getRegistryKey() == net.minecraft.world.World.NETHER || mc.world.getRegistryKey() == net.minecraft.world.World.END)){
                                double oldpercentage = ((double) oldChunkQuantifier / loops) * 100;
                                if (oldpercentage >= beingUpdatedThreshold.get()) chunkIsBeingUpdated = true;
                            }
                            else if (mc.world.getRegistryKey() != net.minecraft.world.World.NETHER && mc.world.getRegistryKey() != net.minecraft.world.World.END){
                                double percentage = ((double) newChunkQuantifier / loops) * 100;
                                if (percentage >= newChunkThreshold.get()) isNewChunk = true;
                            }
                        }
                        
                        if (firstchunkappearsnew) isNewChunk = true;
                        boolean bewlian = (mc.world.getRegistryKey() == net.minecraft.world.World.END) ? isNewChunk : !isOldGeneration;
                        if (isNewChunk && !chunkIsBeingUpdated && bewlian) {
                            classifyAndNotifyChunk(oldpos, ChunkType.NEW);
                            return;
                        }
                        else if (!isNewChunk && !chunkIsBeingUpdated && isOldGeneration) {
                            classifyAndNotifyChunk(oldpos, ChunkType.OLD_GENERATION);
                            return;
                        }
                        else if (chunkIsBeingUpdated) {
                            classifyAndNotifyChunk(oldpos, ChunkType.BEING_UPDATED);
                            return;
                        }
                        else if (!isNewChunk) {
                            classifyAndNotifyChunk(oldpos, ChunkType.OLD);
                            return;
                        }
                    }
                    
                    if (liquidExploit.get()) {
                        for (int x = 0; x < 16; x++) {
                            for (int y = mc.world.getBottomY(); y < mc.world.getTopYInclusive(); y++) {
                                for (int z = 0; z < 16; z++) {
                                    var fluid = chunk.getFluidState(x, y, z);
                                    try {
                                        if (!OldGenerationOldChunks.contains(oldpos) && !beingUpdatedOldChunks.contains(oldpos) && !oldChunks.contains(oldpos) && !tickexploitChunks.contains(oldpos) && !newChunks.contains(oldpos) && !fluid.isEmpty() && !fluid.isStill()) {
                                            classifyAndNotifyChunk(oldpos, ChunkType.OLD);
                                            return;
                                        }
                                    } catch (Exception e) {}
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // Handle exception for outer try block
            }
        }
    }
}