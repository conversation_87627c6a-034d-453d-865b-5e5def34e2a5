package dev.journey.Skylandia.modules.render;

import dev.journey.Skylandia.events.DamageEvent;
import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.render.NametagUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class DamageNumbersModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    
    private final Setting<Integer> duration = sgGeneral.add(new IntSetting.Builder()
        .name("duration-ms")
        .description("How long damage numbers are visible (ms).")
        .defaultValue(800)
        .min(100)
        .sliderMax(2000)
        .build());
        
    private final Setting<Boolean> showCritical = sgGeneral.add(new BoolSetting.Builder()
        .name("show-critical")
        .description("Show critical hits differently.")
        .defaultValue(true)
        .build());
        
    private final Setting<SettingColor> color = sgGeneral.add(new ColorSetting.Builder()
        .name("color")
        .description("Damage number color.")
        .defaultValue(new SettingColor(255, 50, 50, 255))
        .build());
        
    private final Setting<SettingColor> critColor = sgGeneral.add(new ColorSetting.Builder()
        .name("critical-color")
        .description("Critical hit color.")
        .defaultValue(new SettingColor(255, 255, 50, 255))
        .build());
        
    private final Setting<Double> yOffset = sgGeneral.add(new DoubleSetting.Builder()
        .name("y-offset")
        .description("Vertical offset above entity.")
        .defaultValue(1.2)
        .min(0.5)
        .sliderMax(3.0)
        .build());
        
    private final Setting<Boolean> filterPlayers = sgGeneral.add(new BoolSetting.Builder()
        .name("filter-players")
        .description("Show damage numbers only for players.")
        .defaultValue(false)
        .build());

    private final Map<Entity, List<DamageNumber>> activeNumbers = new ConcurrentHashMap<>();

    public DamageNumbersModule() {
        super(Skylandia.Render, "Damage Numbers", "Shows floating damage numbers above entities when they take damage.");
    }

    @EventHandler
    private void onDamage(DamageEvent event) {
        if (filterPlayers.get() && !event.isPlayer()) return;
        DamageNumber dn = new DamageNumber(event.getAmount(), event.isCritical(), System.currentTimeMillis());
        activeNumbers.computeIfAbsent(event.getEntity(), k -> new ArrayList<>()).add(dn);
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        long now = System.currentTimeMillis();
        MinecraftClient mc = MinecraftClient.getInstance();
        Iterator<Map.Entry<Entity, List<DamageNumber>>> it = activeNumbers.entrySet().iterator();
        
        while (it.hasNext()) {
            Map.Entry<Entity, List<DamageNumber>> entry = it.next();
            Entity entity = entry.getKey();
            
            if (!entity.isAlive()) {
                it.remove();
                continue;
            }
            
            List<DamageNumber> numbers = entry.getValue();
            numbers.removeIf(dn -> now - dn.timestamp > duration.get());
            
            if (numbers.isEmpty()) {
                it.remove();
                continue;
            }
            
            Vec3d pos = entity.getPos().add(0, entity.getHeight() + yOffset.get(), 0);
            int i = 0;
            
            for (DamageNumber dn : numbers) {
                double progress = MathHelper.clamp((now - dn.timestamp) / (double) duration.get(), 0, 1);
                double yAnim = progress * 0.8;
                int alpha = (int) (255 * (1.0 - progress));
                Color col = dn.critical && showCritical.get() ? new Color(critColor.get()) : new Color(color.get());
                Color renderCol = new Color(col.r, col.g, col.b, alpha);
                Vec3d textPos = new Vec3d(pos.x, pos.y + yAnim + i * 0.3, pos.z);
                
                if (NametagUtils.to2D(textPos, 1.0)) {
                    mc.textRenderer.draw(
                        event.matrices,
                        String.format("%.1f", dn.amount),
                        (float) textPos.x, (float) textPos.y,
                        renderCol.getPacked()
                    );
                }
                i++;
            }
        }
    }

    private static class DamageNumber {
        public final float amount;
        public final boolean critical;
        public final long timestamp;

        public DamageNumber(float amount, boolean critical, long timestamp) {
            this.amount = amount;
            this.critical = critical;
            this.timestamp = timestamp;
        }
    }
}
