package dev.journey.Skylandia.mixin;

import dev.journey.Skylandia.events.DamageEvent;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.Entity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import dev.journey.Skylandia.Skylandia;
import static meteordevelopment.meteorclient.MeteorClient.EVENT_BUS;

@Mixin(LivingEntity.class)
public class EntityDamageMixin {

    @Inject(method = "damage", at = @At("HEAD"))
    private void onDamage(net.minecraft.entity.damage.DamageSource source, float amount, CallbackInfo ci) {
        LivingEntity entity = (LivingEntity)(Object)this;
        boolean isCritical = source.isSourceCreativePlayer() || "explosion".equals(source.getName());
        boolean isPlayer = entity instanceof PlayerEntity;
        DamageEvent event = new DamageEvent(entity, amount, isCritical, isPlayer);
        EVENT_BUS.post(event);
    }
}