package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.entity.ProjectileEntitySimulator;
import meteordevelopment.meteorclient.utils.misc.Pool;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.passive.PassiveEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.network.packet.c2s.play.PlayerActionC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import net.minecraft.network.packet.c2s.play.UpdateSelectedSlotC2SPacket;
import net.minecraft.screen.slot.SlotActionType;
import org.joml.Vector3d;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;


public class Tridentus extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTargeting = settings.createGroup("Targeting");
    private final SettingGroup sgAiming = settings.createGroup("Aiming");
    private final SettingGroup sgTiming = settings.createGroup("Timing");
    private final SettingGroup sgInventory = settings.createGroup("Inventory");
    private final SettingGroup sgTrajectory = settings.createGroup("Trajectory");
    private final SettingGroup sgOptimization = settings.createGroup("Optimization");
    private final SettingGroup sgAdvanced = settings.createGroup("Advanced");
// === AutoGap Integration ===
private final SettingGroup sgGapGeneral = settings.createGroup("Gap General");
private final SettingGroup sgGapPotions = settings.createGroup("Gap Potions");
private final SettingGroup sgGapHealth = settings.createGroup("Gap Health");

// General
private final Setting<Boolean> allowEgap = sgGapGeneral.add(new BoolSetting.Builder()
    .name("allow-egap")
    .description("Allow eating E-Gaps over Gaps if found.")
    .defaultValue(true)
    .build()
);

private final Setting<Boolean> alwaysEat = sgGapGeneral.add(new BoolSetting.Builder()
    .name("always-eat")
    .description("If it should always eat.")
    .defaultValue(false)
    .build()
);

private final Setting<Boolean> pauseAuras = sgGapGeneral.add(new BoolSetting.Builder()
    .name("pause-auras")
    .description("Pauses all auras when eating.")
    .defaultValue(true)
    .build()
);

private final Setting<Boolean> pauseBaritone = sgGapGeneral.add(new BoolSetting.Builder()
    .name("pause-baritone")
    .description("Pause baritone when eating.")
    .defaultValue(true)
    .build()
);

// Potions
private final Setting<Boolean> potionsRegeneration = sgGapPotions.add(new BoolSetting.Builder()
    .name("potions-regeneration")
    .description("If it should eat when Regeneration runs out.")
    .defaultValue(false)
    .build()
);

private final Setting<Boolean> potionsFireResistance = sgGapPotions.add(new BoolSetting.Builder()
    .name("potions-fire-resistance")
    .description("If it should eat when Fire Resistance runs out. Requires E-Gaps.")
    .defaultValue(true)
    .visible(allowEgap::get)
    .build()
);

private final Setting<Boolean> potionsResistance = sgGapPotions.add(new BoolSetting.Builder()
    .name("potions-absorption")
    .description("If it should eat when Resistance runs out. Requires E-Gaps.")
    .defaultValue(false)
    .visible(allowEgap::get)
    .build()
);

// Health
private final Setting<Boolean> healthEnabled = sgGapHealth.add(new BoolSetting.Builder()
    .name("health-enabled")
    .description("If it should eat when health drops below threshold.")
    .defaultValue(true)
    .build()
);

private final Setting<Integer> healthThreshold = sgGapHealth.add(new IntSetting.Builder()
    .name("health-threshold")
    .description("Health threshold to eat at. Includes absorption.")
    .defaultValue(20)
    .min(0)
    .sliderMax(40)
    .build()
);

// AutoGap state
private boolean gapRequiresEgap;
private boolean gapEating;
private int gapSlot, gapPrevSlot;
private final List<Class<? extends Module>> gapWasAura = new ArrayList<>();
private boolean gapWasBaritone;

    // General Settings
    private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
            .name("Range")
            .description("Maximum targeting range")
            .defaultValue(50)
            .min(1)
            .sliderMax(100)
            .build()
    );

    private final Setting<Boolean> requireUseKey = sgGeneral.add(new BoolSetting.Builder()
            .name("Require Use Key")
            .description("Only attack when holding the use key (right-click)")
            .defaultValue(false)
            .build()
    );

    private final Setting<Boolean> autoAttack = sgGeneral.add(new BoolSetting.Builder()
            .name("Auto Attack")
            .description("Automatically attack valid targets without requiring key input")
            .defaultValue(true)
            .build()
    );

    // Targeting Settings
    private final Setting<TargetMode> targetMode = sgTargeting.add(new EnumSetting.Builder<TargetMode>()
            .name("Target Mode")
            .description("What entities to target")
            .defaultValue(TargetMode.HostileOnly)
            .build()
    );

    private final Setting<Set<EntityType<?>>> entities = sgTargeting.add(new EntityTypeListSetting.Builder()
            .name("Entities")
            .description("Select specific entities to target")
            .visible(() -> targetMode.get() == TargetMode.Custom)
            .defaultValue(EntityType.ZOMBIE, EntityType.SKELETON, EntityType.CREEPER)
            .build()
    );

    private final Setting<TargetPriority> targetPriority = sgTargeting.add(new EnumSetting.Builder<TargetPriority>()
            .name("Target Priority")
            .description("How to prioritize targets")
            .defaultValue(TargetPriority.Closest)
            .build()
    );

    private final Setting<Integer> maxTargetCount = sgTargeting.add(new IntSetting.Builder()
            .name("Max Target Count")
            .description("Maximum number of targets to track for optimal selection")
            .defaultValue(10)
            .min(1)
            .max(50)
            .sliderMax(20)
            .build()
    );

    private final Setting<Boolean> smartTargeting = sgTargeting.add(new BoolSetting.Builder()
            .name("Smart Targeting")
            .description("Use advanced target selection with health and distance filtering")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> targetPlayers = sgTargeting.add(new BoolSetting.Builder()
            .name("Target Players")
            .description("Target other players")
            .defaultValue(false)
            .visible(() -> targetMode.get() == TargetMode.All || targetMode.get() == TargetMode.Custom)
            .build()
    );

    private final Setting<Boolean> ignoreBabies = sgTargeting.add(new BoolSetting.Builder()
            .name("Ignore Babies")
            .description("Don't target baby mobs")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> debugTargeting = sgTargeting.add(new BoolSetting.Builder()
            .name("Debug Targeting")
            .description("Show debug messages for targeting (helps identify issues)")
            .defaultValue(false)
            .build()
    );

    // Aiming Settings
    private final Setting<Double> aimSmoothness = sgAiming.add(new DoubleSetting.Builder()
            .name("Aim Smoothness")
            .description("How smoothly to aim at targets (0.1 = slow, 1.0 = instant)")
            .defaultValue(0.3)
            .min(0.1)
            .max(1.0)
            .sliderMax(1.0)
            .build()
    );

    private final Setting<Boolean> predictMovement = sgAiming.add(new BoolSetting.Builder()
            .name("Predict Movement")
            .description("Try to predict where moving targets will be")
            .defaultValue(true)
            .build()
    );

    private final Setting<Double> predictionMultiplier = sgAiming.add(new DoubleSetting.Builder()
            .name("Prediction Multiplier")
            .description("How much to predict target movement")
            .defaultValue(1.0)
            .min(0.1)
            .max(3.0)
            .sliderMax(3.0)
            .visible(predictMovement::get)
            .build()
    );

    private final Setting<Boolean> dropCompensation = sgAiming.add(new BoolSetting.Builder()
            .name("Drop Compensation")
            .description("Compensate for trident drop over distance")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> serverSideRotation = sgAiming.add(new BoolSetting.Builder()
            .name("Server-Side Rotation")
            .description("Send rotation packets to server for better accuracy")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> clientPrediction = sgAiming.add(new BoolSetting.Builder()
            .name("Client Prediction")
            .description("Use client-side prediction for smoother aiming")
            .defaultValue(true)
            .visible(serverSideRotation::get)
            .build()
    );

    private final Setting<Double> aimAccuracy = sgAiming.add(new DoubleSetting.Builder()
            .name("Aim Accuracy")
            .description("Accuracy multiplier for aiming calculations")
            .defaultValue(1.0)
            .min(0.1)
            .max(2.0)
            .sliderMax(2.0)
            .build()
    );

    // Timing Settings
    private final Setting<Integer> chargeTime = sgTiming.add(new IntSetting.Builder()
            .name("Charge Time")
            .description("How long to charge trident before throwing (ticks)")
            .defaultValue(10)
            .min(1)
            .max(100)
            .sliderMax(50)
            .build()
    );

    private final Setting<Integer> cooldownTicks = sgTiming.add(new IntSetting.Builder()
            .name("Cooldown")
            .description("Delay between throws (ticks)")
            .defaultValue(2)
            .min(0)
            .max(20)
            .sliderMax(20)
            .build()
    );

    // Multi-Trident Settings
    private final Setting<Boolean> multiTrident = sgTiming.add(new BoolSetting.Builder()
            .name("Multi-Trident")
            .description("Throw multiple tridents in rapid succession")
            .defaultValue(false)
            .build()
    );

    private final Setting<Integer> tridentCount = sgTiming.add(new IntSetting.Builder()
            .name("Trident Count")
            .description("Number of tridents to throw per burst")
            .defaultValue(3)
            .min(1)
            .max(9)
            .sliderMax(9)
            .visible(multiTrident::get)
            .build()
    );

    private final Setting<Integer> burstDelay = sgTiming.add(new IntSetting.Builder()
            .name("Burst Delay")
            .description("Delay between each trident in a burst (ticks)")
            .defaultValue(1)
            .min(0)
            .max(10)
            .sliderMax(10)
            .visible(multiTrident::get)
            .build()
    );

    private final Setting<Integer> burstCooldown = sgTiming.add(new IntSetting.Builder()
            .name("Burst Cooldown")
            .description("Cooldown between bursts (ticks)")
            .defaultValue(20)
            .min(5)
            .max(100)
            .sliderMax(100)
            .visible(multiTrident::get)
            .build()
    );

    private final Setting<Boolean> rapidFire = sgTiming.add(new BoolSetting.Builder()
            .name("Rapid Fire")
            .description("Skip charge time for multi-trident (faster but less damage)")
            .defaultValue(true)
            .visible(multiTrident::get)
            .build()
    );

    private final Setting<Boolean> packetBurst = sgTiming.add(new BoolSetting.Builder()
            .name("Packet Burst")
            .description("Use packet-level optimization for ultra-fast burst mode")
            .defaultValue(false)
            .visible(multiTrident::get)
            .build()
    );

    private final Setting<Integer> packetDelay = sgTiming.add(new IntSetting.Builder()
            .name("Packet Delay")
            .description("Delay between packets in burst mode (milliseconds)")
            .defaultValue(50)
            .min(10)
            .max(500)
            .sliderMax(200)
            .visible(() -> multiTrident.get() && packetBurst.get())
            .build()
    );

    private final Setting<Boolean> autoRefill = sgInventory.add(new BoolSetting.Builder()
            .name("Auto Refill")
            .description("Automatically refill hotbar with tridents during burst")
            .defaultValue(true)
            .visible(multiTrident::get)
            .build()
    );

    // Trident Dupe Integration Settings
    private final Setting<Boolean> autoDupe = sgInventory.add(new BoolSetting.Builder()
            .name("Auto Dupe")
            .description("Automatically dupe tridents when running low")
            .defaultValue(false)
            .build()
    );

    private final Setting<Integer> minTridentCount = sgInventory.add(new IntSetting.Builder()
            .name("Min Trident Count")
            .description("Minimum number of tridents before triggering auto-dupe")
            .defaultValue(3)
            .min(1)
            .max(9)
            .sliderMax(9)
            .visible(autoDupe::get)
            .build()
    );

    private final Setting<Integer> targetTridentCount = sgInventory.add(new IntSetting.Builder()
            .name("Target Trident Count")
            .description("Target number of tridents to dupe to")
            .defaultValue(9)
            .min(2)
            .max(36)
            .sliderMax(36)
            .visible(autoDupe::get)
            .build()
    );

    private final Setting<Double> dupeDelay = sgInventory.add(new DoubleSetting.Builder()
            .name("Dupe Delay")
            .description("Delay between dupe attempts (higher = more stable)")
            .defaultValue(5.0)
            .min(1.0)
            .max(20.0)
            .sliderMax(20.0)
            .visible(autoDupe::get)
            .build()
    );

    private final Setting<Boolean> dupeDurabilityManagement = sgInventory.add(new BoolSetting.Builder()
            .name("Dupe Best Durability")
            .description("Dupe the trident with the best durability")
            .defaultValue(true)
            .visible(autoDupe::get)
            .build()
    );

    // Inventory Settings
    private final Setting<Boolean> autoSwitchSlot = sgInventory.add(new BoolSetting.Builder()
            .name("Auto Switch Slot")
            .description("Automatically switch to trident slot")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> pullFromInventory = sgInventory.add(new BoolSetting.Builder()
            .name("Pull From Inventory")
            .description("Pull tridents from inventory to hotbar")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> preferEnchanted = sgInventory.add(new BoolSetting.Builder()
            .name("Prefer Enchanted")
            .description("Prefer enchanted tridents over unenchanted ones")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> preferHigherDurability = sgInventory.add(new BoolSetting.Builder()
            .name("Prefer Higher Durability")
            .description("Prefer tridents with higher durability")
            .defaultValue(false)
            .build()
    );

    // Advanced Settings
    private final Setting<Boolean> debugMode = sgAdvanced.add(new BoolSetting.Builder()
            .name("Debug Mode")
            .description("Show detailed debug information for targeting and burst systems")
            .defaultValue(false)
            .build()
    );

    private final Setting<Boolean> showTargetList = sgAdvanced.add(new BoolSetting.Builder()
            .name("Show Target List")
            .description("Display the current target tracking list")
            .defaultValue(false)
            .visible(debugMode::get)
            .build()
    );

    private final Setting<Boolean> packetDebug = sgAdvanced.add(new BoolSetting.Builder()
            .name("Packet Debug")
            .description("Show packet sending debug information")
            .defaultValue(false)
            .visible(() -> debugMode.get() && packetBurst.get())
            .build()
    );

    private final Setting<Boolean> skipAnimation = sgAdvanced.add(new BoolSetting.Builder()
            .name("Skip Animation")
            .description("Skip trident charging animation and throw instantly using packets")
            .defaultValue(false)
            .build()
    );

    private final Setting<Integer> packetThrowDelay = sgAdvanced.add(new IntSetting.Builder()
            .name("Packet Throw Delay")
            .description("Delay between packet throws in milliseconds")
            .defaultValue(50)
            .min(10)
            .max(500)
            .sliderMax(200)
            .visible(skipAnimation::get)
            .build()
    );

    // Trajectory Settings
    private final Setting<Boolean> showTrajectory = sgTrajectory.add(new BoolSetting.Builder()
            .name("Show Trajectory")
            .description("Render the predicted trajectory path of tridents")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> onlyWhenTargeting = sgTrajectory.add(new BoolSetting.Builder()
            .name("Only When Targeting")
            .description("Only show trajectory when actively targeting an entity")
            .defaultValue(false)
            .visible(showTrajectory::get)
            .build()
    );

    private final Setting<Integer> simulationSteps = sgTrajectory.add(new IntSetting.Builder()
            .name("Simulation Steps")
            .description("How many steps to simulate the trajectory. Zero for no limit")
            .defaultValue(500)
            .min(0)
            .max(5000)
            .sliderMax(1000)
            .visible(showTrajectory::get)
            .build()
    );

    private final Setting<ShapeMode> trajectoryShapeMode = sgTrajectory.add(new EnumSetting.Builder<ShapeMode>()
            .name("Shape Mode")
            .description("How the trajectory is rendered")
            .defaultValue(ShapeMode.Lines)
            .visible(showTrajectory::get)
            .build()
    );

    private final Setting<SettingColor> trajectoryLineColor = sgTrajectory.add(new ColorSetting.Builder()
            .name("Line Color")
            .description("The color of the trajectory line")
            .defaultValue(new SettingColor(255, 100, 100, 255))
            .visible(showTrajectory::get)
            .build()
    );

    private final Setting<SettingColor> trajectorySideColor = sgTrajectory.add(new ColorSetting.Builder()
            .name("Side Color")
            .description("The color of the trajectory sides")
            .defaultValue(new SettingColor(255, 100, 100, 50))
            .visible(() -> showTrajectory.get() && trajectoryShapeMode.get() != ShapeMode.Lines)
            .build()
    );

    private final Setting<Boolean> showHitPoint = sgTrajectory.add(new BoolSetting.Builder()
            .name("Show Hit Point")
            .description("Highlight where the trident will hit")
            .defaultValue(true)
            .visible(showTrajectory::get)
            .build()
    );

    private final Setting<SettingColor> hitPointColor = sgTrajectory.add(new ColorSetting.Builder()
            .name("Hit Point Color")
            .description("The color of the hit point indicator")
            .defaultValue(new SettingColor(255, 0, 0, 200))
            .visible(() -> showTrajectory.get() && showHitPoint.get())
            .build()
    );

    // Optimization Settings
    private final Setting<Boolean> autoOptimize = sgOptimization.add(new BoolSetting.Builder()
            .name("Auto Optimize")
            .description("Automatically optimize charge time for best speed-to-damage ratio")
            .defaultValue(true)
            .build()
    );

    private final Setting<OptimizationMode> optimizationMode = sgOptimization.add(new EnumSetting.Builder<OptimizationMode>()
            .name("Optimization Mode")
            .description("What to optimize for")
            .defaultValue(OptimizationMode.BALANCED)
            .visible(autoOptimize::get)
            .build()
    );

    private final Setting<Integer> learningPeriod = sgOptimization.add(new IntSetting.Builder()
            .name("Learning Period")
            .description("Number of throws to analyze before optimizing (higher = more accurate)")
            .defaultValue(20)
            .min(5)
            .max(100)
            .sliderMax(50)
            .visible(autoOptimize::get)
            .build()
    );

    private final Setting<Boolean> adaptiveOptimization = sgOptimization.add(new BoolSetting.Builder()
            .name("Adaptive Optimization")
            .description("Continuously adapt optimization based on combat conditions")
            .defaultValue(true)
            .visible(autoOptimize::get)
            .build()
    );

    private final Setting<Double> optimizationSensitivity = sgOptimization.add(new DoubleSetting.Builder()
            .name("Optimization Sensitivity")
            .description("How aggressively to optimize (higher = more aggressive)")
            .defaultValue(1.0)
            .min(0.1)
            .max(3.0)
            .sliderMax(2.0)
            .visible(autoOptimize::get)
            .build()
    );

    private final Setting<Boolean> showOptimizationStats = sgOptimization.add(new BoolSetting.Builder()
            .name("Show Optimization Stats")
            .description("Display optimization statistics and performance metrics")
            .defaultValue(false)
            .visible(autoOptimize::get)
            .build()
    );

    // Instance variables
    private Entity currentTarget = null;
    private int currentCooldownTicks = 0;
    private int lastTargetSwitchTick = 0;

    // Enhanced targeting system
    private final List<TargetInfo> trackedTargets = new CopyOnWriteArrayList<>();
    private int targetUpdateTicks = 0;

    // Multi-trident variables
    private boolean isBursting = false;
    private int burstTridentsThrown = 0;
    private int burstDelayTicks = 0;
    private int burstCooldownTicks = 0;
    private int originalSelectedSlot = -1;

    // Packet burst variables
    private boolean isPacketBursting = false;
    private final List<BurstPacket> queuedPackets = new ArrayList<>();
    private long lastPacketTime = 0;

    // Fast packet throwing variables
    private boolean isPacketThrowing = false;
    private long lastPacketThrowTime = 0;

    // Dupe integration variables
    private boolean isDuping = false;
    private boolean dupeCancel = true;
    private final List<TimedTask> dupeScheduledTasks = new ArrayList<>();
    private int lastTridentCount = 0;
    private int dupeCheckCooldown = 0;

    // Trajectory rendering variables
    private final ProjectileEntitySimulator simulator = new ProjectileEntitySimulator();
    private final Pool<Vector3d> vec3s = new Pool<>(Vector3d::new);
    private final List<TrajectoryPath> trajectoryPaths = new ArrayList<>();

    // Optimization tracking variables
    private final List<ThrowMetrics> throwHistory = new ArrayList<>();
    private long lastThrowStartTime = 0;
    private long lastThrowEndTime = 0;
    private int currentOptimalChargeTime = 20; // Start with full charge
    private double currentDPS = 0.0;
    private double bestDPS = 0.0;
    private int bestChargeTime = 20;
    private int optimizationCycle = 0;
    private boolean isLearning = true;
    private long lastOptimizationTime = 0;
    private double averageThrowTime = 0.0;
    private double averageDamage = 0.0;

    public Tridentus() {
        super(Skylandia.Automation, "Tridentus", "Advanced trident combat system with smart targeting and packet-level burst optimization. Dedicated to Brvtale for his invaluable contributions to the clan - his dedication and expertise have shaped this module into what it is today.");
    }

    @Override
    public void onActivate() {
        // Reset optimization when module is activated
        if (autoOptimize.get()) {
            resetOptimization();
        }
    }

    @Override
    public void onDeactivate() {
        // Clear trajectory paths when module is disabled
        for (TrajectoryPath path : trajectoryPaths) {
            path.clear();
        }
        trajectoryPaths.clear();

        // Reset combat states
        currentTarget = null;
        currentCooldownTicks = 0;
        resetBurstState();
    }

    private void resetOptimization() {
        throwHistory.clear();
        currentOptimalChargeTime = 20;
        currentDPS = 0.0;
        bestDPS = 0.0;
        bestChargeTime = 20;
        optimizationCycle = 0;
        isLearning = true;
        lastOptimizationTime = System.currentTimeMillis();
        averageThrowTime = 0.0;
        averageDamage = 0.0;

        if (showOptimizationStats.get()) {
            info("Optimization system reset - beginning learning phase");
        }
    }

    private void displayOptimizationStats() {
        if (throwHistory.isEmpty()) return;

        StringBuilder stats = new StringBuilder();
        stats.append("§6[Tridentus Optimization]§r ");
        stats.append("DPS: §a").append(String.format("%.1f", currentDPS)).append("§r ");
        stats.append("Charge: §b").append(currentOptimalChargeTime).append("t§r ");
        stats.append("Avg Time: §e").append(String.format("%.0f", averageThrowTime)).append("ms§r ");
        stats.append("Throws: §d").append(throwHistory.size()).append("§r");

        if (isLearning && throwHistory.size() < learningPeriod.get()) {
            stats.append(" §7[Learning: ").append(throwHistory.size()).append("/").append(learningPeriod.get()).append("]§r");
        }

        info(stats.toString());
    }

    private void resetBurstState() {
        isBursting = false;
        isPacketBursting = false;
        burstTridentsThrown = 0;
        burstDelayTicks = 0;
        burstCooldownTicks = 0;
        originalSelectedSlot = -1;
        queuedPackets.clear();
        
        // Reset packet throwing state
        isPacketThrowing = false;
        lastPacketThrowTime = 0;
        
        resetDupeState();
    }

    private void resetDupeState() {
        isDuping = false;
        dupeCancel = true;
        dupeScheduledTasks.clear();
        dupeCheckCooldown = 0;
    }



    // Helper class for dupe timing
    private static class TimedTask {
        private final long executeTime;
        private final Runnable task;

        TimedTask(long executeTime, Runnable task) {
            this.executeTime = executeTime;
            this.task = task;
        }
    }

    // Enhanced target tracking
    private static class TargetInfo {
        private final Entity entity;
        private final double distance;
        private final double health;
        private final long lastUpdate;
        private final double score;

        TargetInfo(Entity entity, double distance, double health, double score) {
            this.entity = entity;
            this.distance = distance;
            this.health = health;
            this.score = score;
            this.lastUpdate = System.currentTimeMillis();
        }

        public boolean isValid() {
            return entity != null && entity.isAlive() && !entity.isRemoved() &&
                   System.currentTimeMillis() - lastUpdate < 5000; // 5 second validity
        }
    }

    // Packet burst system
    private static class BurstPacket {
        private final Object packet;
        private final long executeTime;
        private final PacketType type;

        BurstPacket(Object packet, long executeTime, PacketType type) {
            this.packet = packet;
            this.executeTime = executeTime;
            this.type = type;
        }
    }

    private enum PacketType {
        SLOT_CHANGE,
        INTERACT_ITEM,
        RELEASE_ITEM
    }

    // Trajectory rendering class
    private class TrajectoryPath {
        private final List<Vector3d> points = new ArrayList<>();
        private boolean hitQuad, hitQuadHorizontal;
        private double hitQuadX1, hitQuadY1, hitQuadZ1, hitQuadX2, hitQuadY2, hitQuadZ2;
        private Entity collidingEntity;
        private Vector3d lastPoint;

        public void clear() {
            for (Vector3d point : points) vec3s.free(point);
            points.clear();
            hitQuad = false;
            collidingEntity = null;
            lastPoint = null;
        }

        public void calculate() {
            addPoint();

            for (int i = 0; i < (simulationSteps.get() > 0 ? simulationSteps.get() : Integer.MAX_VALUE); i++) {
                HitResult result = simulator.tick();

                if (result != null) {
                    processHitResult(result);
                    break;
                }

                addPoint();
            }
        }

        private void addPoint() {
            points.add(vec3s.get().set(simulator.pos));
        }

        private void processHitResult(HitResult result) {
            if (result.getType() == HitResult.Type.BLOCK) {
                BlockHitResult r = (BlockHitResult) result;

                hitQuad = true;
                hitQuadX1 = r.getPos().x;
                hitQuadY1 = r.getPos().y;
                hitQuadZ1 = r.getPos().z;
                hitQuadX2 = r.getPos().x;
                hitQuadY2 = r.getPos().y;
                hitQuadZ2 = r.getPos().z;

                if (r.getSide() == Direction.UP || r.getSide() == Direction.DOWN) {
                    hitQuadHorizontal = true;
                    hitQuadX1 -= 0.25;
                    hitQuadZ1 -= 0.25;
                    hitQuadX2 += 0.25;
                    hitQuadZ2 += 0.25;
                }
                else if (r.getSide() == Direction.NORTH || r.getSide() == Direction.SOUTH) {
                    hitQuadHorizontal = false;
                    hitQuadX1 -= 0.25;
                    hitQuadY1 -= 0.25;
                    hitQuadX2 += 0.25;
                    hitQuadY2 += 0.25;
                }
                else {
                    hitQuadHorizontal = false;
                    hitQuadZ1 -= 0.25;
                    hitQuadY1 -= 0.25;
                    hitQuadZ2 += 0.25;
                    hitQuadY2 += 0.25;
                }

                points.add(Utils.set(vec3s.get(), result.getPos()));
            }
            else if (result.getType() == HitResult.Type.ENTITY) {
                collidingEntity = ((EntityHitResult) result).getEntity();
                points.add(Utils.set(vec3s.get(), result.getPos()).add(0, collidingEntity.getHeight() / 2, 0));
            }
        }

        public void render(Render3DEvent event) {
            // Render trajectory path
            for (Vector3d point : points) {
                if (lastPoint != null) {
                    event.renderer.line(lastPoint.x, lastPoint.y, lastPoint.z,
                                       point.x, point.y, point.z, trajectoryLineColor.get());
                }
                lastPoint = point;
            }

            // Render hit point
            if (showHitPoint.get()) {
                if (hitQuad) {
                    if (hitQuadHorizontal) {
                        event.renderer.sideHorizontal(hitQuadX1, hitQuadY1, hitQuadZ1,
                                                    hitQuadX1 + 0.5, hitQuadZ1 + 0.5,
                                                    trajectorySideColor.get(), hitPointColor.get(),
                                                    trajectoryShapeMode.get());
                    } else {
                        event.renderer.sideVertical(hitQuadX1, hitQuadY1, hitQuadZ1,
                                                   hitQuadX2, hitQuadY2, hitQuadZ2,
                                                   trajectorySideColor.get(), hitPointColor.get(),
                                                   trajectoryShapeMode.get());
                    }
                }

                // Render entity hit box
                if (collidingEntity != null) {
                    double x = (collidingEntity.getX() - collidingEntity.prevX) * event.tickDelta;
                    double y = (collidingEntity.getY() - collidingEntity.prevY) * event.tickDelta;
                    double z = (collidingEntity.getZ() - collidingEntity.prevZ) * event.tickDelta;

                    Box box = collidingEntity.getBoundingBox();
                    event.renderer.box(x + box.minX, y + box.minY, z + box.minZ,
                                     x + box.maxX, y + box.maxY, z + box.maxZ,
                                     trajectorySideColor.get(), hitPointColor.get(),
                                     trajectoryShapeMode.get(), 0);
                }
            }
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // === AutoGap Integration ===
        if (gapEating) {
            // If we are eating check if we should still be eating
            if (shouldGapEat()) {
                // Check if the item in current slot is not gap or egap
                if (isNotGapOrEGap(mc.player.getInventory().getStack(gapSlot))) {
                    // If not try finding a new slot
                    int slot = findGapSlot();

                    // If no valid slot was found then stop eating
                    if (slot == -1) {
                        stopGapEating();
                        return;
                    }
                    // Otherwise change to the new slot
                    else {
                        changeGapSlot(slot);
                    }
                }

                // Continue eating
                gapEat();
            }
            // If we shouldn't be eating anymore then stop
            else {
                stopGapEating();
            }
            return; // Pause all other actions while eating
        } else {
            // If we are not eating check if we should start eating
            if (shouldGapEat()) {
                // Try to find a valid slot
                gapSlot = findGapSlot();

                // If slot was found then start eating
                if (gapSlot != -1) startGapEating();
                return; // Pause all other actions while eating
            }
        }

        // --- Non-blocking checks ---
        // Pause if eating
        if (mc.player.isUsingItem() && (mc.player.getMainHandStack().getItem() == Items.GOLDEN_APPLE ||
            mc.player.getMainHandStack().getItem() == Items.ENCHANTED_GOLDEN_APPLE)) return;

        // Pause if mining (breaking block)
        if (mc.interactionManager != null && mc.interactionManager.isBreakingBlock()) return;

        // Pause if interacting with a block (chest, shulker, etc.)
        if (mc.player.currentScreenHandler != null && !mc.player.currentScreenHandler.equals(mc.player.playerScreenHandler)) return;

        // Pause if holding or using ender pearl
        if (mc.player.getMainHandStack().isOf(Items.ENDER_PEARL) && mc.player.isUsingItem()) return;

        // Process dupe scheduled tasks
        processDupeTasks();

        // Process packet burst queue
        processPacketBurst();

        // Update target tracking system
        updateTargetTracking();

        // Check if we should attack (either auto attack is enabled or use key is pressed)
        boolean shouldAttack = autoAttack.get() || (requireUseKey.get() && mc.options.useKey.isPressed());

        if (!shouldAttack) {
            if (debugMode.get()) {
                info("Not attacking: autoAttack=" + autoAttack.get() +
                     ", requireUseKey=" + requireUseKey.get() +
                     ", useKeyPressed=" + mc.options.useKey.isPressed());
            }
            currentTarget = null;
            currentCooldownTicks = 0;
            resetBurstState();
            return;
        }

        // Handle auto-dupe logic
        if (autoDupe.get() && !isDuping) {
            handleAutoDupeCheck();
        }

        // Don't do combat actions while duping
        if (isDuping) return;

        // Check if we have any tridents available
        if (!hasTridentsAvailable()) {
            if (debugMode.get()) {
                info("No tridents available for combat");
            }
            return;
        }

        // Handle multi-trident burst logic
        if (multiTrident.get()) {
            if (packetBurst.get()) {
                handlePacketBurstLogic();
            } else {
                handleMultiTridentLogic();
            }
            return;
        }

        // Handle regular single trident logic
        handleSingleTridentLogic();

        // Display optimization stats periodically
        if (autoOptimize.get() && showOptimizationStats.get() &&
            System.currentTimeMillis() - lastOptimizationTime > 5000) { // Every 5 seconds
            displayOptimizationStats();
        }
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        if (!showTrajectory.get()) return;
        if (mc.player == null || mc.world == null) return;

        // Only show trajectory when conditions are met
        if (onlyWhenTargeting.get() && currentTarget == null) return;

        // Check if player is holding a trident
        ItemStack mainHand = mc.player.getMainHandStack();
        if (!mainHand.isOf(Items.TRIDENT)) return;

        // Calculate and render trajectory
        calculateTrajectory(event.tickDelta);
        renderTrajectory(event);
    }

    private void calculateTrajectory(float tickDelta) {
        // Clear existing paths
        for (TrajectoryPath path : trajectoryPaths) {
            path.clear();
        }

        // Ensure we have at least one path
        if (trajectoryPaths.isEmpty()) {
            trajectoryPaths.add(new TrajectoryPath());
        }

        // Get the trident stack
        ItemStack tridentStack = mc.player.getMainHandStack();
        if (!tridentStack.isOf(Items.TRIDENT)) return;

        // Set up simulator
        if (!simulator.set(mc.player, tridentStack, 0, true, tickDelta)) return;

        // Calculate trajectory
        trajectoryPaths.get(0).calculate();

        if (debugMode.get()) {
            info("Calculated trajectory with " + trajectoryPaths.get(0).points.size() + " points");
        }
    }

    private void renderTrajectory(Render3DEvent event) {
        for (TrajectoryPath path : trajectoryPaths) {
            path.render(event);
        }
    }

    private TrajectoryPath getEmptyPath() {
        for (TrajectoryPath path : trajectoryPaths) {
            if (path.points.isEmpty()) return path;
        }

        TrajectoryPath path = new TrajectoryPath();
        trajectoryPaths.add(path);
        return path;
    }

    private void handleSingleTridentLogic() {
        // Handle cooldown
        if (currentCooldownTicks > 0) {
            currentCooldownTicks--;
            if (debugMode.get() && currentCooldownTicks % 10 == 0) {
                info("Cooldown remaining: " + currentCooldownTicks + " ticks");
            }
            return;
        }

        // Find target
        findTarget();

        if (currentTarget == null) {
            if (debugMode.get()) {
                info("No valid target found");
            }
            return;
        }

        if (debugMode.get()) {
            info("Target acquired: " + getEntityName(currentTarget) +
                 " at distance " + String.format("%.1f", mc.player.distanceTo(currentTarget)));
        }

        // Handle trident in hand
        if (!handleTridentEquipping()) {
            if (debugMode.get()) {
                info("Failed to equip trident");
            }
            return;
        }

        // Aim at target
        aimAtTarget();

        // Handle throwing
        handleThrowing();
    }

    private void processDupeTasks() {
        long currentTime = System.currentTimeMillis();

        // Create a copy of tasks to execute to avoid ConcurrentModificationException
        List<TimedTask> tasksToExecute = new ArrayList<>();

        // Find tasks that are ready to execute
        Iterator<TimedTask> iterator = dupeScheduledTasks.iterator();
        while (iterator.hasNext()) {
            TimedTask task = iterator.next();
            if (task.executeTime <= currentTime) {
                tasksToExecute.add(task);
                iterator.remove(); // Safe removal using iterator
            }
        }

        // Execute tasks outside of the iteration
        for (TimedTask task : tasksToExecute) {
            task.task.run();
        }
    }

    private void handleAutoDupeCheck() {
        // Cooldown between checks
        if (dupeCheckCooldown > 0) {
            dupeCheckCooldown--;
            return;
        }

        int currentTridentCount = countTridents();

        // Check if we need to dupe
        if (currentTridentCount < minTridentCount.get()) {
            info("Low trident count (" + currentTridentCount + "), starting auto-dupe...");
            startAutoDupe();
            dupeCheckCooldown = 100; // 5 second cooldown
        } else {
            dupeCheckCooldown = 20; // 1 second between checks
        }
    }

    private int countTridents() {
        int count = 0;
        // Count tridents in hotbar
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).isOf(Items.TRIDENT)) {
                count++;
            }
        }
        // Count tridents in inventory
        for (int i = 9; i < 36; i++) {
            if (mc.player.getInventory().getStack(i).isOf(Items.TRIDENT)) {
                count++;
            }
        }
        return count;
    }

    private void startAutoDupe() {
        if (isDuping) return;

        isDuping = true;
        dupeCancel = true;

        // Find best trident to dupe
        int bestSlot = findBestTridentForDupe();
        if (bestSlot == -1) {
            error("No suitable trident found for duping");
            isDuping = false;
            return;
        }

        // Switch to the best trident
        mc.player.getInventory().selectedSlot = bestSlot;

        // Start dupe process
        performTridentDupe();
    }

    private int findBestTridentForDupe() {
        int bestSlot = -1;
        int bestDurability = Integer.MAX_VALUE;

        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.isOf(Items.TRIDENT)) {
                if (dupeDurabilityManagement.get()) {
                    // Find trident with best (lowest) damage
                    int damage = stack.getDamage();
                    if (damage < bestDurability) {
                        bestDurability = damage;
                        bestSlot = i;
                    }
                } else {
                    // Just use first trident found
                    return i;
                }
            }
        }

        return bestSlot;
    }

    // === AutoGap Helper Methods ===
    private boolean shouldGapEat() {
        gapRequiresEgap = false;

        if (alwaysEat.get()) return true;
        if (shouldGapEatPotions()) return true;
        return shouldGapEatHealth();
    }

    private boolean shouldGapEatPotions() {
        Map<?, ?> effects = mc.player.getActiveStatusEffects();

        // Regeneration
        if (potionsRegeneration.get() && !effects.containsKey(net.minecraft.entity.effect.StatusEffects.REGENERATION)) return true;

        // Fire resistance
        if (potionsFireResistance.get() && !effects.containsKey(net.minecraft.entity.effect.StatusEffects.FIRE_RESISTANCE)) {
            gapRequiresEgap = true;
            return true;
        }

        // Absorption/Resistance
        if (potionsResistance.get() && !effects.containsKey(net.minecraft.entity.effect.StatusEffects.RESISTANCE)) {
            gapRequiresEgap = true;
            return true;
        }

        return false;
    }

    private boolean shouldGapEatHealth() {
        if (!healthEnabled.get()) return false;

        int health = Math.round(mc.player.getHealth() + mc.player.getAbsorptionAmount());
        return health < healthThreshold.get();
    }

    private int findGapSlot() {
        boolean preferEgap = this.allowEgap.get() || gapRequiresEgap;
        int slot = -1;

        for (int i = 0; i < 9; i++) {
            // Skip if item stack is empty
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.isEmpty()) continue;

            // Skip if item isn't a gap or egap
            if (isNotGapOrEGap(stack)) continue;
            net.minecraft.item.Item item = stack.getItem();

            // If egap was found and preferEgap is true we can return the current slot
            if (item == Items.ENCHANTED_GOLDEN_APPLE && preferEgap) {
                slot = i;
                break;
            }
            // If gap was found and egap is not required we can return the current slot
            else if (item == Items.GOLDEN_APPLE && !gapRequiresEgap) {
                slot = i;
                if (!preferEgap) break;
            }
        }

        return slot;
    }

    private boolean isNotGapOrEGap(ItemStack stack) {
        net.minecraft.item.Item item = stack.getItem();
        return item != Items.GOLDEN_APPLE && item != Items.ENCHANTED_GOLDEN_APPLE;
    }

    private void startGapEating() {
        gapPrevSlot = mc.player.getInventory().selectedSlot;
        gapEat();

        // Pause auras (stub, implement as needed)
        gapWasAura.clear();
        // if (pauseAuras.get()) { ... }

        // Pause baritone (stub, implement as needed)
        gapWasBaritone = false;
        // if (pauseBaritone.get() && PathManagers.get().isPathing()) { ... }
    }

    private void gapEat() {
        changeGapSlot(gapSlot);
        mc.options.useKey.setPressed(true);
        if (!mc.player.isUsingItem()) meteordevelopment.meteorclient.utils.Utils.rightClick();

        gapEating = true;
    }

    private void stopGapEating() {
        changeGapSlot(gapPrevSlot);
        mc.options.useKey.setPressed(false);

        gapEating = false;

        // Resume auras (stub, implement as needed)
        // Resume baritone (stub, implement as needed)
    }

    private void changeGapSlot(int slot) {
        InvUtils.swap(slot, false);
        this.gapSlot = slot;
    }

    private void handleMultiTridentLogic() {
        // Handle burst cooldown
        if (burstCooldownTicks > 0) {
            burstCooldownTicks--;
            return;
        }

        // Find target if not bursting
        if (!isBursting) {
            findTarget();
            if (currentTarget == null) return;

            // Start burst
            isBursting = true;
            burstTridentsThrown = 0;
            burstDelayTicks = 0;
            originalSelectedSlot = mc.player.getInventory().selectedSlot;
        }

        // Handle burst delay
        if (burstDelayTicks > 0) {
            burstDelayTicks--;
            return;
        }

        // Check if burst is complete
        if (burstTridentsThrown >= tridentCount.get()) {
            // End burst
            isBursting = false;
            burstCooldownTicks = burstCooldown.get();

            // Restore original slot if possible
            if (originalSelectedSlot != -1) {
                mc.player.getInventory().selectedSlot = originalSelectedSlot;
                originalSelectedSlot = -1;
            }
            return;
        }

        // Aim at target
        aimAtTarget();

        // Find and equip next trident
        if (!handleMultiTridentEquipping()) {
            // No more tridents available, end burst
            isBursting = false;
            burstCooldownTicks = burstCooldown.get();
            return;
        }

        // Throw trident
        if (handleMultiTridentThrowing()) {
            burstTridentsThrown++;
            burstDelayTicks = burstDelay.get();
        }
    }

    private void updateTargetTracking() {
        if (mc.player == null || mc.world == null) return;

        targetUpdateTicks++;
        if (targetUpdateTicks % 5 != 0) return; // Update every 5 ticks for performance

        // Clear invalid targets
        trackedTargets.removeIf(target -> !target.isValid() ||
                               mc.player.distanceTo(target.entity) > range.get());

        // Scan for new targets
        List<TargetInfo> newTargets = new ArrayList<>();
        for (Entity entity : mc.world.getEntities()) {
            if (entity == null) continue;
            if (entity == mc.player || entity.getUuid().equals(mc.player.getUuid())) continue;
            if (!isValidTarget(entity)) continue;

            double distance = mc.player.distanceTo(entity);
            if (distance > range.get()) continue;

            double health = entity instanceof LivingEntity living ? living.getHealth() : 20.0;
            double score = calculateAdvancedTargetScore(entity, distance, health);

            newTargets.add(new TargetInfo(entity, distance, health, score));
        }

        // Sort by score and keep top targets
        newTargets.sort(Comparator.comparingDouble(t -> t.score));
        trackedTargets.clear();
        trackedTargets.addAll(newTargets.stream()
                .limit(maxTargetCount.get())
                .collect(Collectors.toList()));

        if (debugMode.get() && showTargetList.get() && !trackedTargets.isEmpty()) {
            info("Tracking " + trackedTargets.size() + " targets. Best: " +
                 getEntityName(trackedTargets.get(0).entity) +
                 " (Score: " + String.format("%.2f", trackedTargets.get(0).score) + ")");
        }
    }

    private void findTarget() {
        if (mc.player == null || mc.world == null) {
            currentTarget = null;
            return;
        }

        Entity bestTarget = null;

        if (smartTargeting.get() && !trackedTargets.isEmpty()) {
            // Use smart targeting system
            bestTarget = trackedTargets.get(0).entity;
        } else {
            // Fallback to original targeting
            double bestScore = Double.MAX_VALUE;
            for (Entity entity : mc.world.getEntities()) {
                if (entity == null) continue;
                if (entity == mc.player || entity.getUuid().equals(mc.player.getUuid())) continue;
                if (!isValidTarget(entity)) continue;

                double distance = mc.player.distanceTo(entity);
                if (distance > range.get()) continue;

                double score = calculateTargetScore(entity, distance);
                if (score < bestScore) {
                    bestScore = score;
                    bestTarget = entity;
                }
            }
        }

        // Final safety check before setting target
        if (bestTarget != null && (bestTarget == mc.player || bestTarget.getUuid().equals(mc.player.getUuid()))) {
            if (debugTargeting.get()) {
                error("CRITICAL: Attempted to target self! Blocking target selection.");
            }
            bestTarget = null;
        }

        // Only switch target if significantly better or current target is invalid
        if (bestTarget != currentTarget) {
            if (currentTarget == null || !isValidTarget(currentTarget) ||
                mc.player.distanceTo(currentTarget) > range.get() ||
                (mc.world.getTime() - lastTargetSwitchTick > 20)) { // Min 1 second between switches

                currentTarget = bestTarget;
                lastTargetSwitchTick = (int) mc.world.getTime();

                if (debugTargeting.get()) {
                    if (bestTarget != null) {
                        String targetName = bestTarget instanceof PlayerEntity ?
                            ((PlayerEntity) bestTarget).getGameProfile().getName() :
                            bestTarget.getType().toString();
                        info("Target switched to: " + targetName + " at distance " +
                             String.format("%.1f", mc.player.distanceTo(bestTarget)));
                    } else {
                        info("Target cleared (no valid targets found)");
                    }
                }
            }
        }
    }

    private boolean isValidTarget(Entity entity) {
        // Null check
        if (entity == null) return false;

        // Must be a living entity
        if (!(entity instanceof LivingEntity)) return false;

        // CRITICAL: Never target the player running the module
        if (entity == mc.player) return false;

        // Additional safety check using UUID comparison
        if (mc.player != null && entity.getUuid().equals(mc.player.getUuid())) {
            return false;
        }

        // Must be alive
        if (!entity.isAlive()) return false;

        // Don't target entities that are being removed
        if (entity.isRemoved()) return false;

        // Check baby mobs
        if (ignoreBabies.get() && entity instanceof LivingEntity living && living.isBaby()) {
            return false;
        }

        // Check target mode
        switch (targetMode.get()) {
            case HostileOnly:
                return entity instanceof HostileEntity;
            case PassiveOnly:
                return entity instanceof PassiveEntity;
            case Players:
                // For player-only mode, ensure it's a player AND not the module runner
                if (entity instanceof PlayerEntity player) {
                    return targetPlayers.get() && player != mc.player;
                }
                return false;
            case All:
                if (entity instanceof PlayerEntity player) {
                    return targetPlayers.get() && player != mc.player;
                }
                return true;
            case Custom:
                if (entity instanceof PlayerEntity player) {
                    return targetPlayers.get() && player != mc.player;
                }
                return entities.get().contains(entity.getType());
        }
        return false;
    }

    private void recordThrowMetrics(int actualChargeTime) {
        if (lastThrowStartTime == 0 || lastThrowEndTime == 0 || currentTarget == null) return;

        double distance = mc.player.distanceTo(currentTarget);
        double estimatedDamage = calculateEstimatedDamage(actualChargeTime, distance);
        boolean hit = true; // Assume hit for now, could be enhanced with actual hit detection

        ThrowMetrics metrics = new ThrowMetrics(
            lastThrowStartTime,
            lastThrowEndTime,
            actualChargeTime,
            estimatedDamage,
            distance,
            hit
        );

        throwHistory.add(metrics);

        // Limit history size for performance
        if (throwHistory.size() > learningPeriod.get() * 2) {
            throwHistory.remove(0);
        }

        // Update optimization if we have enough data
        if (throwHistory.size() >= learningPeriod.get()) {
            updateOptimization();
        }

        if (showOptimizationStats.get()) {
            info("Throw recorded: " + String.format("%.1f", metrics.getDPS()) + " DPS, " +
                 actualChargeTime + " ticks, " + String.format("%.1f", distance) + " blocks");
        }
    }

    private double calculateEstimatedDamage(int chargeTime, double distance) {
        // Base damage calculation for tridents
        // Minimum damage: 4.0, Maximum damage: 9.0
        // Damage increases with charge time up to 20 ticks
        double chargeFactor = Math.min(1.0, chargeTime / 20.0);
        double baseDamage = 4.0 + (5.0 * chargeFactor);

        // Distance falloff (tridents lose damage over distance)
        double distanceFactor = Math.max(0.5, 1.0 - (distance * 0.02));

        return baseDamage * distanceFactor;
    }

    private void updateOptimization() {
        if (throwHistory.size() < learningPeriod.get()) return;

        // Get recent throws for analysis
        List<ThrowMetrics> recentThrows = throwHistory.subList(
            Math.max(0, throwHistory.size() - learningPeriod.get()),
            throwHistory.size()
        );

        // Calculate current performance
        currentDPS = recentThrows.stream()
            .mapToDouble(ThrowMetrics::getDPS)
            .average()
            .orElse(0.0);

        averageThrowTime = recentThrows.stream()
            .mapToDouble(m -> m.totalTime)
            .average()
            .orElse(0.0);

        averageDamage = recentThrows.stream()
            .mapToDouble(m -> m.estimatedDamage)
            .average()
            .orElse(0.0);

        // Determine optimal charge time based on optimization mode
        int newOptimalChargeTime = findOptimalChargeTime(recentThrows);

        // Apply optimization sensitivity
        double sensitivity = optimizationSensitivity.get();
        if (Math.abs(newOptimalChargeTime - currentOptimalChargeTime) > 1) {
            int adjustment = (int) Math.round((newOptimalChargeTime - currentOptimalChargeTime) * sensitivity);
            currentOptimalChargeTime = Math.max(1, Math.min(20, currentOptimalChargeTime + adjustment));
        }

        // Update best performance tracking
        if (currentDPS > bestDPS) {
            bestDPS = currentDPS;
            bestChargeTime = currentOptimalChargeTime;
        }

        // Adaptive learning
        if (adaptiveOptimization.get()) {
            adaptOptimization();
        }

        lastOptimizationTime = System.currentTimeMillis();

        if (showOptimizationStats.get()) {
            info("Optimization updated: " + currentOptimalChargeTime + " ticks, " +
                 String.format("%.1f", currentDPS) + " DPS (best: " + String.format("%.1f", bestDPS) + ")");
        }
    }

    private int findOptimalChargeTime(List<ThrowMetrics> throwMetrics) {
        switch (optimizationMode.get()) {
            case SPEED:
                // Minimize charge time while maintaining reasonable damage
                return throwMetrics.stream()
                    .filter(m -> m.estimatedDamage >= 6.0) // Minimum damage threshold
                    .mapToInt(m -> m.chargeTime)
                    .min()
                    .orElse(10);

            case DAMAGE:
                // Maximize damage regardless of speed
                return throwMetrics.stream()
                    .max((a, b) -> Double.compare(a.estimatedDamage, b.estimatedDamage))
                    .map(m -> m.chargeTime)
                    .orElse(20);

            case BALANCED:
                // Optimize for DPS
                return throwMetrics.stream()
                    .max((a, b) -> Double.compare(a.getDPS(), b.getDPS()))
                    .map(m -> m.chargeTime)
                    .orElse(15);

            case ADAPTIVE:
                // Adapt based on combat situation
                double avgDistance = throwMetrics.stream()
                    .mapToDouble(m -> m.distance)
                    .average()
                    .orElse(10.0);

                if (avgDistance > 15.0) {
                    // Long range: prioritize damage
                    return Math.max(15, 18);
                } else {
                    // Close range: prioritize speed
                    return Math.min(12, 8);
                }

            default:
                return 15;
        }
    }

    private void adaptOptimization() {
        // Continuously adapt based on performance trends
        if (throwHistory.size() < learningPeriod.get() * 2) return;

        // Compare recent performance to historical performance
        int halfPoint = throwHistory.size() / 2;
        List<ThrowMetrics> oldThrows = throwHistory.subList(0, halfPoint);
        List<ThrowMetrics> newThrows = throwHistory.subList(halfPoint, throwHistory.size());

        double oldDPS = oldThrows.stream().mapToDouble(ThrowMetrics::getDPS).average().orElse(0.0);
        double newDPS = newThrows.stream().mapToDouble(ThrowMetrics::getDPS).average().orElse(0.0);

        // If performance is declining, try different optimization
        if (newDPS < oldDPS * 0.95) { // 5% tolerance
            optimizationCycle++;

            // Cycle through different charge times to find better performance
            if (optimizationCycle % 3 == 0) {
                currentOptimalChargeTime = Math.max(1, currentOptimalChargeTime - 2);
            } else if (optimizationCycle % 3 == 1) {
                currentOptimalChargeTime = Math.min(20, currentOptimalChargeTime + 2);
            } else {
                currentOptimalChargeTime = bestChargeTime; // Return to best known
            }

            if (showOptimizationStats.get()) {
                info("Adaptive optimization: trying charge time " + currentOptimalChargeTime);
            }
        }
    }

    private double calculateTargetScore(Entity entity, double distance) {
        double score = distance; // Base score on distance

        switch (targetPriority.get()) {
            case Closest:
                return distance;
            case LowestHealth:
                if (entity instanceof LivingEntity living) {
                    return living.getHealth();
                }
                return distance;
            case HighestHealth:
                if (entity instanceof LivingEntity living) {
                    return 100 - living.getHealth(); // Invert health for priority
                }
                return distance;
            case Players:
                if (entity instanceof PlayerEntity) {
                    return distance * 0.5; // Prioritize players
                }
                return distance * 2; // Deprioritize non-players
            case NearestPlayer:
                if (entity instanceof PlayerEntity) {
                    return distance * 0.3; // Highest priority for players
                }
                return distance * 3; // Much lower priority for non-players
            case NearestMob:
                if (entity instanceof PlayerEntity) {
                    return distance * 3; // Lower priority for players
                }
                return distance * 0.3; // Higher priority for mobs
            case LowestHealthPlayer:
                if (entity instanceof PlayerEntity player) {
                    return ((LivingEntity) player).getHealth() * 0.5;
                }
                return distance * 5; // Very low priority for non-players
            case LowestHealthMob:
                if (entity instanceof PlayerEntity) {
                    return distance * 5; // Very low priority for players
                }
                if (entity instanceof LivingEntity living) {
                    return living.getHealth() * 0.5;
                }
                return distance;
        }
        return score;
    }

    private double calculateAdvancedTargetScore(Entity entity, double distance, double health) {
        double score = distance;

        // Apply priority modifiers
        switch (targetPriority.get()) {
            case Closest:
            case NearestPlayer:
            case NearestMob:
                score = distance;
                break;
            case LowestHealth:
            case LowestHealthPlayer:
            case LowestHealthMob:
                score = health + (distance * 0.1); // Prioritize health but consider distance
                break;
            case HighestHealth:
                score = (100 - health) + (distance * 0.1);
                break;
            case Players:
                if (entity instanceof PlayerEntity) {
                    score = distance * 0.5;
                } else {
                    score = distance * 2;
                }
                break;
        }

        // Apply type-specific modifiers
        if (targetPriority.get() == TargetPriority.NearestPlayer ||
            targetPriority.get() == TargetPriority.LowestHealthPlayer) {
            if (!(entity instanceof PlayerEntity)) {
                score *= 10; // Heavily penalize non-players
            }
        } else if (targetPriority.get() == TargetPriority.NearestMob ||
                   targetPriority.get() == TargetPriority.LowestHealthMob) {
            if (entity instanceof PlayerEntity) {
                score *= 10; // Heavily penalize players
            }
        }

        return score;
    }

    private String getEntityName(Entity entity) {
        if (entity instanceof PlayerEntity player) {
            return player.getGameProfile().getName();
        }
        return entity.getType().toString();
    }

    private boolean hasTridentsAvailable() {
        // Check main hand
        if (mc.player.getMainHandStack().isOf(Items.TRIDENT)) {
            return true;
        }

        // Check hotbar
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).isOf(Items.TRIDENT)) {
                return true;
            }
        }

        // Check inventory if pull from inventory is enabled
        if (pullFromInventory.get()) {
            for (int i = 9; i < mc.player.getInventory().size(); i++) {
                if (mc.player.getInventory().getStack(i).isOf(Items.TRIDENT)) {
                    return true;
                }
            }
        }

        return false;
    }

    private boolean handleTridentEquipping() {
        ItemStack mainHand = mc.player.getMainHandStack();

        if (mainHand.isOf(Items.TRIDENT)) {
            if (debugMode.get()) {
                info("Already holding trident");
            }
            return true; // Already holding trident
        }

        if (!autoSwitchSlot.get()) {
            if (debugMode.get()) {
                info("Auto switch slot disabled, cannot equip trident");
            }
            return false;
        }

        // Find trident in hotbar
        int bestSlot = findBestTridentSlot(false);
        if (bestSlot != -1) {
            if (debugMode.get()) {
                info("Switching to trident in slot " + bestSlot);
            }
            mc.player.getInventory().selectedSlot = bestSlot;
            return true;
        }

        // Pull from inventory if enabled
        if (pullFromInventory.get()) {
            if (debugMode.get()) {
                info("Attempting to pull trident from inventory");
            }
            return pullTridentFromInventory();
        }

        if (debugMode.get()) {
            info("No trident found in hotbar and pull from inventory disabled");
        }
        return false;
    }

    private int findBestTridentSlot(boolean includeInventory) {
        int bestSlot = -1;
        int bestScore = -1;
        
        int start = includeInventory ? 0 : 0;
        int end = includeInventory ? mc.player.getInventory().size() : 9;

        for (int i = start; i < end; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (!stack.isOf(Items.TRIDENT)) continue;

            int score = calculateTridentScore(stack);
            if (score > bestScore) {
                bestScore = score;
                bestSlot = i;
            }
        }
        
        return bestSlot;
    }

    private int calculateTridentScore(ItemStack stack) {
        int score = 0;
        
        // Prefer enchanted tridents
        if (preferEnchanted.get() && !stack.getEnchantments().isEmpty()) {
            score += 100;
        }
        
        // Consider durability
        if (preferHigherDurability.get() && stack.isDamageable()) {
            score += stack.getMaxDamage() - stack.getDamage();
        }
        
        return score;
    }

    private boolean pullTridentFromInventory() {
        int bestInventorySlot = findBestTridentSlot(true);
        if (bestInventorySlot == -1 || bestInventorySlot < 9) return false;

        // Find empty hotbar slot
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).isEmpty()) {
                InvUtils.move().from(bestInventorySlot).to(i);
                mc.player.getInventory().selectedSlot = i;
                return true;
            }
        }
        
        return false;
    }

    private boolean handleMultiTridentEquipping() {
        // Check if current slot has trident
        ItemStack mainHand = mc.player.getMainHandStack();
        if (mainHand.isOf(Items.TRIDENT)) {
            return true;
        }

        // Find next available trident in hotbar
        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.isOf(Items.TRIDENT)) {
                mc.player.getInventory().selectedSlot = i;
                return true;
            }
        }

        // Auto-refill hotbar with tridents if enabled
        if (autoRefill.get() && pullFromInventory.get()) {
            if (pullTridentFromInventory()) {
                return true;
            }
        }

        // If auto-dupe is enabled and we're low on tridents, trigger dupe
        if (autoDupe.get() && !isDuping && countTridents() < minTridentCount.get()) {
            info("Multi-trident mode triggered auto-dupe due to low trident count");
            startAutoDupe();
        }

        return false;
    }

    private boolean handleMultiTridentThrowing() {
        if (!mc.player.getMainHandStack().isOf(Items.TRIDENT)) return false;

        // CRITICAL SAFETY CHECK: Never throw if targeting ourselves
        if (currentTarget == null || currentTarget == mc.player ||
            currentTarget.getUuid().equals(mc.player.getUuid())) {
            if (debugTargeting.get() && currentTarget == mc.player) {
                error("CRITICAL: Blocked multi-trident throw at self!");
            }
            currentTarget = null;
            return false;
        }

        if (rapidFire.get()) {
            // Rapid fire mode - instant throws
            if (!mc.player.isUsingItem()) {
                mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
            }

            // Immediately release for rapid fire
            if (mc.player.isUsingItem()) {
                mc.interactionManager.stopUsingItem(mc.player);
                return true;
            }
        } else {
            // Charged mode - use charge time
            if (!mc.player.isUsingItem()) {
                mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
            } else if (mc.player.getItemUseTime() >= chargeTime.get()) {
                mc.interactionManager.stopUsingItem(mc.player);
                return true;
            }
        }

        return false;
    }

    private void performTridentDupe() {
        int delayMs = (int) (dupeDelay.get() * 100);
        int currentCount = countTridents();

        // Check if we've reached target count
        if (currentCount >= targetTridentCount.get()) {
            info("Reached target trident count (" + currentCount + "), stopping dupe");
            isDuping = false;
            return;
        }

        // Start the dupe sequence
        mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
        dupeCancel = true;

        scheduleDupeTask(() -> {
            dupeCancel = false;

            // Perform the dupe sequence (based on TridentDupe module)
            if (mc.player.currentScreenHandler != null) {
                // Swap to offhand
                mc.interactionManager.clickSlot(mc.player.currentScreenHandler.syncId, 3, 0, SlotActionType.SWAP, mc.player);

                // Send release packet
                PlayerActionC2SPacket packet = new PlayerActionC2SPacket(
                    PlayerActionC2SPacket.Action.RELEASE_USE_ITEM,
                    BlockPos.ORIGIN,
                    Direction.DOWN,
                    0
                );
                mc.getNetworkHandler().sendPacket(packet);
            }

            dupeCancel = true;

            // Schedule next dupe attempt
            scheduleDupeTask(() -> {
                if (isDuping && countTridents() < targetTridentCount.get()) {
                    performTridentDupe();
                } else {
                    isDuping = false;
                    info("Auto-dupe completed. Total tridents: " + countTridents());
                }
            }, delayMs);
        }, delayMs);
    }

    private void scheduleDupeTask(Runnable task, long delayMs) {
        dupeScheduledTasks.add(new TimedTask(System.currentTimeMillis() + delayMs, task));
    }

    private void processPacketBurst() {
        if (!packetBurst.get() || queuedPackets.isEmpty()) return;

        long currentTime = System.currentTimeMillis();
        Iterator<BurstPacket> iterator = queuedPackets.iterator();

        while (iterator.hasNext()) {
            BurstPacket packet = iterator.next();
            if (packet.executeTime <= currentTime) {
                sendBurstPacket(packet);
                iterator.remove();
            }
        }
    }

    private void sendBurstPacket(BurstPacket burstPacket) {
        if (mc.getNetworkHandler() == null) return;

        try {
            switch (burstPacket.type) {
                case SLOT_CHANGE:
                    if (burstPacket.packet instanceof UpdateSelectedSlotC2SPacket) {
                        mc.getNetworkHandler().sendPacket((UpdateSelectedSlotC2SPacket) burstPacket.packet);
                        if (packetDebug.get()) {
                            info("Sent slot change packet");
                        }
                    }
                    break;
                case INTERACT_ITEM:
                    if (burstPacket.packet instanceof PlayerInteractItemC2SPacket) {
                        mc.getNetworkHandler().sendPacket((PlayerInteractItemC2SPacket) burstPacket.packet);
                        if (packetDebug.get()) {
                            info("Sent interact item packet");
                        }
                    }
                    break;
                case RELEASE_ITEM:
                    if (burstPacket.packet instanceof PlayerActionC2SPacket) {
                        mc.getNetworkHandler().sendPacket((PlayerActionC2SPacket) burstPacket.packet);
                        if (packetDebug.get()) {
                            info("Sent release item packet");
                        }
                    }
                    break;
            }
        } catch (Exception e) {
            if (debugMode.get()) {
                error("Failed to send burst packet: " + e.getMessage());
            }
        }
    }

    private void handlePacketBurstLogic() {
        // Handle burst cooldown
        if (burstCooldownTicks > 0) {
            burstCooldownTicks--;
            return;
        }

        // Find target if not bursting
        if (!isPacketBursting) {
            findTarget();
            if (currentTarget == null) return;

            // Start packet burst
            isPacketBursting = true;
            burstTridentsThrown = 0;
            originalSelectedSlot = mc.player.getInventory().selectedSlot;

            // Queue all burst packets at once
            queueBurstPackets();

            if (debugMode.get()) {
                info("Starting packet burst with " + tridentCount.get() + " tridents");
            }
        }

        // Check if burst is complete
        if (burstTridentsThrown >= tridentCount.get()) {
            // End burst
            isPacketBursting = false;
            burstCooldownTicks = burstCooldown.get();
            queuedPackets.clear();

            // Restore original slot
            if (originalSelectedSlot != -1) {
                mc.player.getInventory().selectedSlot = originalSelectedSlot;
                originalSelectedSlot = -1;
            }

            if (debugMode.get()) {
                info("Packet burst completed");
            }
        }
    }

    private void queueBurstPackets() {
        if (mc.player == null) return;

        long baseTime = System.currentTimeMillis();
        int delay = packetDelay.get();

        for (int i = 0; i < tridentCount.get(); i++) {
            int tridentSlot = findNextTridentSlot(i);
            if (tridentSlot == -1) break;

            long packetTime = baseTime + (i * delay);

            // Queue slot change
            queuedPackets.add(new BurstPacket(
                new UpdateSelectedSlotC2SPacket(tridentSlot),
                packetTime,
                PacketType.SLOT_CHANGE
            ));

            // Queue interact item (start charging)
            queuedPackets.add(new BurstPacket(
                new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, 0, 0.0f, 0.0f),
                packetTime + 10,
                PacketType.INTERACT_ITEM
            ));

            // Queue release item (throw)
            queuedPackets.add(new BurstPacket(
                new PlayerActionC2SPacket(
                    PlayerActionC2SPacket.Action.RELEASE_USE_ITEM,
                    BlockPos.ORIGIN,
                    Direction.DOWN,
                    0
                ),
                packetTime + (rapidFire.get() ? 20 : chargeTime.get() * 50),
                PacketType.RELEASE_ITEM
            ));

            burstTridentsThrown++;
        }

        if (packetDebug.get()) {
            info("Queued " + queuedPackets.size() + " packets for burst");
        }
    }

    private int findNextTridentSlot(int index) {
        int slotsChecked = 0;
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).isOf(Items.TRIDENT)) {
                if (slotsChecked == index) {
                    return i;
                }
                slotsChecked++;
            }
        }
        return -1;
    }

    private void aimAtTarget() {
        if (currentTarget == null) return;

        // CRITICAL SAFETY CHECK: Never aim at ourselves
        if (currentTarget == mc.player || currentTarget.getUuid().equals(mc.player.getUuid())) {
            if (debugTargeting.get()) {
                error("CRITICAL: Attempted to aim at self! Clearing target.");
            }
            currentTarget = null;
            return;
        }

        // Get enhanced target position
        Vec3d targetPos = calculateOptimalAimPoint();
        if (targetPos == null) return;

        // Calculate aim angles with enhanced accuracy
        double dx = targetPos.x - mc.player.getX();
        double dz = targetPos.z - mc.player.getZ();
        double dy = targetPos.y - (mc.player.getY() + mc.player.getEyeHeight(mc.player.getPose()));

        double horizontalDist = Math.sqrt(dx * dx + dz * dz);

        // Enhanced drop compensation
        if (dropCompensation.get()) {
            double dropComp = calculateDropCompensation(horizontalDist);
            dy += dropComp;
        }

        float targetYaw = (float) Math.toDegrees(Math.atan2(dz, dx)) - 90f;
        float targetPitch = (float) -Math.toDegrees(Math.atan2(dy, horizontalDist));

        // Apply accuracy multiplier
        targetYaw *= aimAccuracy.get().floatValue();
        targetPitch *= aimAccuracy.get().floatValue();

        // Apply smoothing
        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        float smoothness = aimSmoothness.get().floatValue();
        float smoothYaw = currentYaw + (targetYaw - currentYaw) * smoothness;
        float smoothPitch = currentPitch + (targetPitch - currentPitch) * smoothness;

        // Apply rotations
        if (serverSideRotation.get()) {
            // Send rotation to server
            Rotations.rotate(smoothYaw, smoothPitch, () -> {
                if (clientPrediction.get()) {
                    // Also update client-side for smooth visuals
                    mc.player.setYaw(smoothYaw);
                    mc.player.setPitch(smoothPitch);
                }
            });
        } else {
            // Client-side only
            mc.player.setYaw(smoothYaw);
            mc.player.setPitch(smoothPitch);
        }

        if (debugMode.get()) {
            info("Aiming at " + getEntityName(currentTarget) +
                 " - Yaw: " + String.format("%.1f", smoothYaw) +
                 ", Pitch: " + String.format("%.1f", smoothPitch));
        }
    }

    private Vec3d calculateOptimalAimPoint() {
        if (currentTarget == null) return null;

        // Get target position
        double targetX = currentTarget.getX();
        double targetY = currentTarget.getY() + currentTarget.getHeight() / 2.0;
        double targetZ = currentTarget.getZ();

        // Enhanced movement prediction
        if (predictMovement.get() && currentTarget instanceof LivingEntity) {
            double distance = mc.player.distanceTo(currentTarget);
            double predictionTime = distance / 25.0; // More accurate prediction time

            Vec3d velocity = currentTarget.getVelocity();
            double multiplier = predictionMultiplier.get();

            targetX += velocity.x * predictionTime * multiplier;
            targetY += velocity.y * predictionTime * multiplier;
            targetZ += velocity.z * predictionTime * multiplier;
        }

        return new Vec3d(targetX, targetY, targetZ);
    }

    private double calculateDropCompensation(double horizontalDistance) {
        // More accurate drop compensation based on trident physics
        double gravity = 0.05;
        double velocity = 2.5;
        double time = horizontalDistance / velocity;
        return gravity * time * time * 0.5;
    }

    private void handleThrowing() {
        if (!mc.player.getMainHandStack().isOf(Items.TRIDENT)) {
            if (debugMode.get()) {
                info("No trident in main hand, cannot throw");
            }
            return;
        }

        // Only block if targeting self
        if (currentTarget == null) {
            if (debugMode.get()) {
                warning("No target selected, cannot throw trident.");
            }
            return;
        }
        if (currentTarget == mc.player || currentTarget.getUuid().equals(mc.player.getUuid())) {
            if (debugTargeting.get()) {
                error("CRITICAL: Blocked trident throw at self! Target: " + getEntityName(currentTarget));
            }
            currentTarget = null;
            return;
        }

        // Use packet throwing if skip animation is enabled
        if (skipAnimation.get()) {
            handlePacketThrowing();
            return;
        }

        // Get optimal charge time from optimization system
        int optimalChargeTime = autoOptimize.get() ? currentOptimalChargeTime : chargeTime.get();

        // Start charging the trident
        if (!mc.player.isUsingItem()) {
            lastThrowStartTime = System.currentTimeMillis();
            if (debugMode.get()) {
                info("Starting trident charge for target: " + getEntityName(currentTarget) +
                     " (optimal charge: " + optimalChargeTime + " ticks)");
            }
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
        }
        // Release when optimally charged
        else if (mc.player.getItemUseTime() >= optimalChargeTime) {
            lastThrowEndTime = System.currentTimeMillis();

            // Record throw metrics for optimization
            if (autoOptimize.get()) {
                recordThrowMetrics(mc.player.getItemUseTime());
            }

            if (debugMode.get()) {
                info("Releasing trident at target: " + getEntityName(currentTarget) +
                     " (charged for " + mc.player.getItemUseTime() + " ticks, optimal: " + optimalChargeTime + ")");
            }

            mc.interactionManager.stopUsingItem(mc.player);
            currentCooldownTicks = cooldownTicks.get();
        }
        // Debug charging progress
        else if (debugMode.get() && mc.player.getItemUseTime() % 5 == 0) {
            info("Charging trident: " + mc.player.getItemUseTime() + "/" + optimalChargeTime + " ticks");
        }
    }

    private void handlePacketThrowing() {
        long currentTime = System.currentTimeMillis();

        // Check delay between packet throws
        if (currentTime - lastPacketThrowTime < packetThrowDelay.get()) {
            return;
        }

        // Only block if targeting self
        if (currentTarget == null) {
            if (debugMode.get()) {
                warning("No target selected, cannot throw trident (packet mode).");
            }
            return;
        }
        if (currentTarget == mc.player || currentTarget.getUuid().equals(mc.player.getUuid())) {
            if (debugTargeting.get()) {
                error("SAFETY: Prevented packet throw at self! Target: " + getEntityName(currentTarget));
            }
            currentTarget = null;
            return;
        }

        if (mc.getNetworkHandler() == null) {
            if (debugMode.get()) {
                warning("Network handler is null, cannot send packets");
            }
            return;
        }

        try {
            // Send interact packet to start using trident
            PlayerInteractItemC2SPacket interactPacket = new PlayerInteractItemC2SPacket(
                Hand.MAIN_HAND, 
                (int) mc.world.getTime(),
                mc.player.getYaw(),
                mc.player.getPitch()
            );
            mc.getNetworkHandler().sendPacket(interactPacket);

            // Immediately send stop using packet to release instantly
            PlayerActionC2SPacket releasePacket = new PlayerActionC2SPacket(
                PlayerActionC2SPacket.Action.RELEASE_USE_ITEM,
                BlockPos.ORIGIN,
                Direction.DOWN
            );
            mc.getNetworkHandler().sendPacket(releasePacket);

            lastPacketThrowTime = currentTime;
            lastThrowStartTime = currentTime;
            lastThrowEndTime = currentTime;
            currentCooldownTicks = cooldownTicks.get();

            // Record metrics for optimization (assuming instant throw = 1 tick charge)
            if (autoOptimize.get()) {
                recordThrowMetrics(1);
            }

            if (debugMode.get()) {
                info("Packet throw executed at target: " + getEntityName(currentTarget));
            }

        } catch (Exception e) {
            if (debugMode.get()) {
                error("Failed to send packet throw: " + e.getMessage());
            }
        }
    }

    // Enums for settings
    public enum TargetMode {
        HostileOnly("Hostile Only"),
        PassiveOnly("Passive Only"),
        Players("Players Only"),
        All("All Entities"),
        Custom("Custom List");

        private final String name;

        TargetMode(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    public enum TargetPriority {
        Closest("Closest"),
        LowestHealth("Lowest Health"),
        HighestHealth("Highest Health"),
        Players("Players First"),
        NearestPlayer("Nearest Player"),
        NearestMob("Nearest Mob"),
        LowestHealthPlayer("Lowest Health Player"),
        LowestHealthMob("Lowest Health Mob");

        private final String name;

        TargetPriority(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    public enum OptimizationMode {
        SPEED("Max Speed"),
        DAMAGE("Max Damage"),
        BALANCED("Balanced DPS"),
        ADAPTIVE("Adaptive");

        private final String name;

        OptimizationMode(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    // Metrics tracking for optimization
    private static class ThrowMetrics {
        private final long startTime;
        private final long endTime;
        private final int chargeTime;
        private final double estimatedDamage;
        private final double distance;
        private final boolean hit;
        private final long totalTime;

        ThrowMetrics(long startTime, long endTime, int chargeTime, double estimatedDamage, double distance, boolean hit) {
            this.startTime = startTime;
            this.endTime = endTime;
            this.chargeTime = chargeTime;
            this.estimatedDamage = estimatedDamage;
            this.distance = distance;
            this.hit = hit;
            this.totalTime = endTime - startTime;
        }

        public double getDPS() {
            if (totalTime <= 0) return 0.0;
            return (estimatedDamage * 1000.0) / totalTime; // DPS calculation
        }

        public double getThrowsPerSecond() {
            if (totalTime <= 0) return 0.0;
            return 1000.0 / totalTime;
        }

        public double getEfficiency() {
            // Efficiency = (damage / max_damage) * (speed / max_speed)
            double damageRatio = estimatedDamage / 9.0; // Max trident damage is 9
            double speedRatio = Math.min(1.0, 1000.0 / totalTime); // Normalize speed
            return damageRatio * speedRatio;
        }
    }
}